name: Daily test suite

on:
  workflow_dispatch:
  schedule:
    - cron: "0 5 * * *"

jobs:
  call-workflow-ci:
    if: github.repository == 'voedger/voedger'
    uses: untillpro/ci-action/.github/workflows/ci_reuse_go.yml@master
    with:
      ignore_copyright: "cmd/voedger/sys.monitor/site.main"
      go_race: "true"
      short_test: "false"
      ignore_build: "true"
      test_subfolders: "true" 
    secrets:
      reporeading_token: ${{ secrets.REPOREADING_TOKEN }}
      codecov_token: ${{ secrets.CODECOV_TOKEN }}
      personal_token: ${{ secrets.PERSONAL_TOKEN }}

  notify_failure:
    needs: call-workflow-ci
    if: ${{ failure() }}
    runs-on: ubuntu-22.04
    outputs:
      failure_url: ${{ steps.set_output.outputs.failure_url }} # Declaring output at the job level
    steps:
      - name: Set Failure URL Output
        id: set_output
        run: echo "failure_url=https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}" >> $GITHUB_OUTPUT

  call-workflow-create-issue:
    needs: notify_failure
    if: ${{ failure() }}
    uses: untillpro/ci-action/.github/workflows/create_issue.yml@master
    with:
      repo: "voedger/voedger"
      assignee: "host6"
      name: "Daily Test failed on"
      body: ${{ needs.notify_failure.outputs.failure_url }}
      label: "prty/blocker"
    secrets:
      personaltoken: ${{ secrets.PERSONAL_TOKEN }}

  call-workflow-vulncheck:
    needs: call-workflow-ci
    uses: voedger/voedger/.github/workflows/ci-vulncheck.yml@main
  call-workflow-cd-voeger:
    needs: call-workflow-vulncheck
    if: ${{ contains(github.repository, 'voedger/voedger' ) }}
    uses: voedger/voedger/.github/workflows/cd-voedger.yml@main
    secrets:
      dockerusername: ${{ secrets.DOCKER_USERNAME }}
      dockerpassword: ${{ secrets.DOCKER_PASSWORD }}
      personaltoken: ${{ secrets.PERSONAL_TOKEN }}
      reporeading_token: ${{ secrets.REPOREADING_TOKEN }}
