// Code generated by "stringer -type=ResourceKindType"; DO NOT EDIT.

package istructs

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ResourceKind_null-0]
	_ = x[ResourceKind_CommandFunction-1]
	_ = x[ResourceKind_QueryFunction-2]
	_ = x[ResourceKind_FakeLast-3]
}

const _ResourceKindType_name = "ResourceKind_nullResourceKind_CommandFunctionResourceKind_QueryFunctionResourceKind_FakeLast"

var _ResourceKindType_index = [...]uint8{0, 17, 45, 71, 92}

func (i ResourceKindType) String() string {
	if i >= ResourceKindType(len(_ResourceKindType_index)-1) {
		return "ResourceKindType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ResourceKindType_name[_ResourceKindType_index[i]:_ResourceKindType_index[i+1]]
}
