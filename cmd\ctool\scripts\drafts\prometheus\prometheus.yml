global:
  scrape_interval:     30s # By default, scrape targets every 1 minute.
  evaluation_interval: 30s # By default, evaluate rules every 1 minute.
  external_labels:
     node: {{.Label}}  # mon1 for example. Use label as node.label.type in swarm
  #scrape_timeout:     10s # By default, a scrape request times out in 10 seconds.
  
# Alertmanager configuration
alerting:
  alert_relabel_configs:
    - source_labels: [node]
      regex: (.+)\d+
      target_label: node
  alertmanagers:
    - static_configs:
        - targets:
            - {{.AppNode1}}:9093 # during deploy replace with real ip address of alert manager
            - {{.AppNode2}}:9093 # during deploy replace with real ip address of alert manager

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert.rules"

  # Configuring Prometheus to monitor itself - https://prometheus.io/docs/prometheus/latest/getting_started/
scrape_configs:
  - job_name: 'prometheus'
    # Override the global values and scrape targets for this job every 10 seconds.
    scrape_interval: 10s
    static_configs:
      # Execute query expressions prometheus_abc_xyz
      - targets: 
        - {{.AppNode1}}:9090
        - {{.AppNode2}}:9090

  - job_name: 'node-exporter'
    scrape_interval: 10s
    static_configs:
      # node-exporter:9100 where node-exporter is service name in docker-compose.yml 
      # Execute non-prometheus_abc_xyz query expressions, e.g., node_load1 etc.
      - targets: 
        - {{.AppNode1}}:9100
        - {{.AppNode2}}:9100
        - {{.DBNode1}}:9500
        - {{.DBNode2}}:9500
        - {{.DBNode3}}:9500

  - job_name: 'scylla-cluster'
    scrape_interval: 10s
    static_configs:
      # Monitor scylla nodes with embedded scylla exporter
      - targets: 
        - {{.DBNode1}}:9180
        - {{.DBNode2}}:9180
        - {{.DBNode3}}:9180

  - job_name: 'cadvisor'
    scrape_interval: 10s
    static_configs:
      # Monitor swarm nodes and services
      - targets: 
        - {{.AppNode1}}:8080
        - {{.AppNode2}}:8080
        
  - job_name: 'voedger'
    scrape_interval: 10s
    static_configs:
      - targets:
        - {{.AppNode1}}:8000
        - {{.AppNode2}}:8000
