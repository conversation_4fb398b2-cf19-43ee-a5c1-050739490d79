name: Merge PR

on:
  workflow_call:
    secrets:
      personaltoken:
        required: true

jobs: 
  build:
    runs-on: ubuntu-22.04

    steps:

    - name: Merge PR
      env: 
        GH_TOKEN: ${{ secrets.personaltoken }}
        token: ${{ secrets.personaltoken }}
        repo: ${{ GITHUB.REPOSITORY }}
        pr_number: ${{ github.event.number }}
        br_name: ${{ github.head_ref || github.ref_name }}
      run: curl -s https://raw.githubusercontent.com/untillpro/ci-action/master/scripts/domergepr.sh | bash 
