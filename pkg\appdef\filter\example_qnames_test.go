/*
 * Copyright (c) 2024-present Sigma-Soft, Ltd.
 * @author: <PERSON><PERSON>
 */

package filter_test

import (
	"fmt"

	"github.com/voedger/voedger/pkg/appdef"
	"github.com/voedger/voedger/pkg/appdef/builder"
	"github.com/voedger/voedger/pkg/appdef/filter"
)

func ExampleQNames() {
	fmt.Println("This example demonstrates how to work with the QNames filter")
	fmt.Println()

	wsName := appdef.NewQName("test", "workspace")
	doc, obj, cmd := appdef.NewQName("test", "doc"), appdef.NewQName("test", "object"), appdef.NewQName("test", "command")

	app := func() appdef.IAppDef {
		adb := builder.New()
		adb.AddPackage("test", "test.com/test")

		wsb := adb.AddWorkspace(wsName)

		_ = wsb.AddODoc(doc)
		_ = wsb.AddObject(obj)
		_ = wsb.AddCommand(cmd)

		return adb.MustBuild()
	}()

	ws := app.Workspace(wsName)

	example := func(flt appdef.IFilter) {
		fmt.Println(flt)
		fmt.Println("- kind:", flt.Kind())
		fmt.Println("- QNames:")
		for _, n := range flt.QNames() {
			fmt.Println("  *", n)
		}
		fmt.Println("- testing:")
		for _, t := range ws.LocalTypes() {
			fmt.Println("  *", t, "is matched:", flt.Match(t))
		}
		fmt.Println()
	}

	example(filter.QNames(doc, obj))
	example(filter.QNames(appdef.NewQName("test", "unknown")))

	// Output:
	// This example demonstrates how to work with the QNames filter
	//
	// QNAMES(test.doc, test.object)
	// - kind: FilterKind_QNames
	// - QNames:
	//   * test.doc
	//   * test.object
	// - testing:
	//   * BuiltIn-Command «test.command» is matched: false
	//   * ODoc «test.doc» is matched: true
	//   * Object «test.object» is matched: true
	//
	// QNAMES(test.unknown)
	// - kind: FilterKind_QNames
	// - QNames:
	//   * test.unknown
	// - testing:
	//   * BuiltIn-Command «test.command» is matched: false
	//   * ODoc «test.doc» is matched: false
	//   * Object «test.object» is matched: false
}
