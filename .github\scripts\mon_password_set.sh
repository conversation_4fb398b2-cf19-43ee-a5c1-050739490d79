#!/usr/bin/env bash
# Copyright (c) 2024 Sigma-Soft, Ltd.
# <AUTHOR> Ponomarev

set -euo pipefail

set -x

MON_PASSWORD=$1

ctool_cmd="cd /home/<USER>/voedger/cmd/ctool && ./ctool mon password -v "

if [ "$ISSUE_TITLE" == "ctoolintegrationtest se" -o "$ISSUE_TITLE" == "ctoolintegrationtest se3" ]; then
    ctool_cmd+="--ssh-key /tmp/amazonKey.pem "
fi
ctool_cmd+="$MON_PASSWORD"

IFS=' ' read -r -a ssh_options_args <<< "$SSH_OPTIONS"

ssh "${ssh_options_args[@]}" ubuntu@"$CTOOL_IP" "$ctool_cmd"
