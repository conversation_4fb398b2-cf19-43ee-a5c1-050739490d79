
receivers:
  - name: 'discord'
    discord_configs:
      - webhook_url: "[[.Alert.DiscordWebhook]]"
        send_resolved: true
        title: '{{ template "discord.default.title" . }}'
        message: '{{ template "discord.default.message" . }}'

  - name: 'ctool_discord'
    discord_configs:
      - webhook_url: "[[.Alert.DiscordWebhook]]"
        send_resolved: false
        title: '{{ template "discord.default.title" . }}'
        message: '{{ template "discord.default.message" . }}'

route:
  receiver: discord
  group_by: ['source', 'instance', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  routes:
    - match:
        source: 'ctool'
      receiver: 'ctool_discord'
