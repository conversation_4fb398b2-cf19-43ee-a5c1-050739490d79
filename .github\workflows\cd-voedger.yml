name: CD cmd/voedger

on:
  workflow_call:
    secrets:
      dockerusername:
        required: true
      dockerpassword:
        required: true
      personaltoken:
        required: true
      reporeading_token:
        required: true

jobs:
  build:
    runs-on: ubuntu-22.04

    steps:

    - name: Checkout
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'
        cache: false

    - name: Build executable
      run: |
        git config --global url."https://${{ secrets.REPOREADING_TOKEN }}:<EMAIL>/heeus".insteadOf "https://github.com/heeus"
        git config --global url."https://${{ secrets.REPOREADING_TOKEN }}:<EMAIL>/untillpro".insteadOf "https://github.com/untillpro"
        git config --global url."https://${{ secrets.REPOREADING_TOKEN }}:<EMAIL>/voedger".insteadOf "https://github.com/voedger"
        go build -o ./cmd/voedger ./cmd/voedger
      env:
        GOPRIVATE: "github.com/untillpro/*,github.com/heeus/*,github.com/voedger/*"
        CGO_ENABLED: 0

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.dockerusername }}
        password: ${{ secrets.dockerpassword }}

    - name: Build and push Docker image alpha
      uses: docker/build-push-action@v5
      with:
        context: ./cmd/voedger
        file: ./cmd/voedger/Dockerfile
        push: true
        tags: voedger/voedger:0.0.1-alpha
