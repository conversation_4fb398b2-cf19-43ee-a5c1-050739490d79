<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Slack" id="id-d7a9359a8380494c8a639226a7a19fed" version="4.9.0">
  <folder name="Strategy" id="id-5021f98d13d5466f9ea0864d8f262783" type="strategy"/>
  <folder name="Business" id="id-c98724065352419984bb80189d797a39" type="business">
    <folder name="Stories" id="id-ea1b7144a3054933b27329d08fef5c33">
      <folder name="10-Workspaces" id="id-58fd880ecef3412cbbed352b8f01dab6">
        <element xsi:type="archimate:BusinessProcess" name="Create a workspace" id="id-300e708a9443430c8d684c6b10d3ae1a"/>
        <element xsi:type="archimate:BusinessProcess" name="List channels" id="id-7598f55992474e05b0a20f6e3d147ca2">
          <documentation>- &quot;Перечислить каналы&quot;&#xD;
</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="List DM" id="id-853543b687cf4312a8c65e16e56bd0ae" profiles="id-3194ebd31afc4e32af0182acf4f435f1">
          <documentation>https://superuser.com/questions/1199066/why-is-my-slack-dm-list-incomplete&#xD;
I can confirm that what you’re experiencing is expected behavior, which I’m happy explain. Once you have more than 11 direct messages (DMs) or group DMs open, any additional direct messages are expected to fall off the list if the DM is not opened during your current session, there has been no activity in the DM in the past week, or there are no unread messages in the DM.</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="Workspace AuthZ" id="id-bb2501d1a706447b9b42614d20bf1133"/>
        <element xsi:type="archimate:BusinessProcess" name="List unread channels" id="id-f268e699daf640a7b3d243ba93b6dff5"/>
        <element xsi:type="archimate:BusinessProcess" name="List channels with mentions" id="id-e7b329e279424ace8f66e52a075e5157">
          <documentation>- Упоминания читаются из профиля</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="N10n: changes in channels???" id="id-83156a1b9a6b4bd38f292f471ae6987d">
          <documentation>- Message/reply added/changed</documentation>
        </element>
      </folder>
      <folder name="Search" id="id-c1496214fdb14c0284f22ea2a72d0b03">
        <element xsi:type="archimate:BusinessProcess" name="Search" id="id-a05c991e1d3e46b89780a8b08dab6f31">
          <documentation>https://slack.com/help/articles/202528808-Search-in-Slack&#xD;
&#xD;
- Below the search field, select the type of results you want: messages, files, people, or channels&#xD;
- To narrow your search, you can include modifiers</documentation>
        </element>
      </folder>
      <folder name="20-Channels" id="id-bedb0d57f61343c78db37af63bce15e0">
        <element xsi:type="archimate:BusinessProcess" name="Join a channel" id="id-1ebf0b62da794785b29537b713f21271"/>
        <element xsi:type="archimate:BusinessProcess" name="Leave a channel" id="id-e3f3b9bd8a514c17900191bc112701c4"/>
        <element xsi:type="archimate:BusinessProcess" name="Add people to a channel" id="id-9a05bd515587459e84c46f77b800c9bd"/>
        <element xsi:type="archimate:BusinessProcess" name="Channel AuthZ" id="id-0096ce2aaf7a4c4ebddf37f4f50fa447"/>
        <element xsi:type="archimate:BusinessProcess" name="N10n: Changes in messages" id="id-f97136ca383c4c5ba55def96b436056f">
          <documentation>- Включая mirrored messages (also send to...)&#xD;
&#xD;
Для всех видимых сообщений&#xD;
- Подписываемся на канал&#xD;
- В случае изменений читаем окрестности (neighbors)&#xD;
</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="Create a channel" id="id-a8f2fb04da7f451d80191133b0cb190b"/>
        <element xsi:type="archimate:BusinessProcess" name="Unlink a channel from a workspace" id="id-327d5d6fbf16416fb09fe6edabc42a2f"/>
        <element xsi:type="archimate:BusinessProcess" name="Add a channel to multiple workspaces" id="id-9de7ca305c8b43518b0bc486a29b8762">
          <documentation>??? How to search archive copy after disconnect&#xD;
</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="Convert a channel to private" id="id-677dc48b5df1449288d2d11c8b64c28d"/>
        <element xsi:type="archimate:BusinessProcess" name="Remove your organization or other organizations from a channel???" id="id-1e0e21cd85eb4318950f1163c0791ac6">
          <documentation>https://slack.com/help/articles/360026489273-Remove-your-organization-or-other-organizations-from-a-channel-&#xD;
&#xD;
The company that owns the channel can continue using it. Organizations that are removed or remove themselves will keep an archived copy of the channel’s contents.&#xD;
</documentation>
        </element>
      </folder>
      <folder name="Format &amp; style messages" id="id-dd71ef98813c40c9836be50ede892b35">
        <folder name="Use mentions in Slack" id="id-012a92e4b23648569dc2662e8e86216b">
          <element xsi:type="archimate:BusinessProcess" name="Implicitly mention the user in the DM" id="id-450e2c51776943e78ae759ed8f52b732">
            <documentation>https://www.jetbrains.com/help/youtrack/standalone/Mention-Another-User.html&#xD;
</documentation>
          </element>
          <element xsi:type="archimate:BusinessProcess" name="Mention someone in a message" id="id-eadaa81ee72a4c9dae4865fbb84db167">
            <documentation>- Упоминания копируются в профиль&#xD;
- Пользователь следит за изменениями в профиле&#xD;
</documentation>
          </element>
          <element xsi:type="archimate:BusinessProcess" name="Mention not a member" id="id-1ddd72835b154b0c8809afb0e13f65af">
            <documentation>- Отвечает bot&#xD;
- Ответ виден только одному принципалу&#xD;
- Особенности реализации: дырки в ID не смущают, так как могут образовываться из-за удалений&#xD;
</documentation>
          </element>
        </folder>
        <folder name="Send and read messages" id="id-20af27e80de34d828604a8b1cd3613f0">
          <element xsi:type="archimate:BusinessProcess" name="Send messages" id="id-7b06bd79bc5049bbb6ad39259485590b"/>
          <element xsi:type="archimate:BusinessProcess" name="Send messages (drafts)" id="id-cee6e64c51ca407da1dd31d9d1afe55f"/>
          <element xsi:type="archimate:BusinessProcess" name="Read messages" id="id-9bae6adc7a2a46f3b03f5455f5a750f8">
            <documentation>https://slack.com/help/articles/201457107-Send-and-read-messages&#xD;
If a channel or direct message name is bolded in your channel list, that means there are unread messages. You can click the bolded channel or direct message to open the conversation and view unread messages.&#xD;
&#xD;
&#xD;
# Implementation&#xD;
&#xD;
 frame&#xD;
- Top&#xD;
- Neighbors (Окрестности)&#xD;
</documentation>
          </element>
        </folder>
        <folder name="Edit or delete messages" id="id-2f177687d2f046668c2dd70e3408dc4c">
          <documentation>https://slack.com/help/articles/202395258-Edit-or-delete-messages</documentation>
          <element xsi:type="archimate:BusinessProcess" name="Edit a message" id="id-f8bf0d7cf05842db893fdc4f0b12425c"/>
          <element xsi:type="archimate:BusinessProcess" name="Delete a message" id="id-ecd2bbd224d04c16a5646448422571ba">
            <documentation>By default, any member can delete their messages, but owners and admins can restrict this permission. If you can't delete a message, find an owner or admin to ask for help. Owners and admins can also delete members’ messages in public channels, private channels, and group direct messages (DMs) they’re part of.</documentation>
          </element>
        </folder>
        <element xsi:type="archimate:BusinessProcess" name="Use emoji and reactions" id="id-6c22cc487ed14f0ea1db04aea4164786"/>
      </folder>
      <folder name="Posts" id="id-8d982185cf1e4a0583cce539778ce217">
        <element xsi:type="archimate:BusinessFunction" name="Use posts in Slack" id="id-7e311166da3a48dc9f975c36437b05b3">
          <documentation>https://slack.com/help/articles/*********-Use-posts-in-Slack</documentation>
        </element>
      </folder>
      <folder name="30-Direct messages" id="id-01a9a73983af44928271d015d273855d">
        <element xsi:type="archimate:BusinessProcess" name="Use Slack Connect to start a DM with someone at another company" id="id-c9d3542bc4464887bf9290882fdc4073"/>
        <element xsi:type="archimate:BusinessProcess" name="End direct messages with people from other organizations" id="id-27a2e9af458a4412991ee920d3128c95"/>
        <element xsi:type="archimate:BusinessProcess" name="Convert a group direct message to a private channel" id="id-a617cbd409ca4373a1c034f7c789c44e"/>
        <element xsi:type="archimate:BusinessProcess" name="Add more people to a group direct message" id="id-13ce664697174c65a1aab1d7da77c65a"/>
      </folder>
      <folder name="Threads" id="id-7b4d0f4b77464cd68a2dc7c9c967a381">
        <element xsi:type="archimate:BusinessProcess" name="Send reply to a channel" id="id-c7b5e39bc1534f35806f7ce52df537b3">
          <documentation>Create a reply linked to a message  (mirror message to reply)&#xD;
&#xD;
https://slack.com/help/articles/115000769927-Use-threads-to-organize-discussions-</documentation>
        </element>
        <element xsi:type="archimate:BusinessProcess" name="View all threads???" id="id-01403a0a0ee74b119fef659542d338ff">
          <documentation>https://slack.com/help/articles/115000769927-Use-threads-to-organize-discussions-&#xD;
&#xD;
- Threads at the top of your left sidebar to see all the conversations you’re following&#xD;
- Threads with unread replies will appear at the top of the list</documentation>
        </element>
      </folder>
      <folder name="N10n (show)" id="id-f4f7a64002554e05bd6c78b0f78cd5b2">
        <documentation>https://slack.com/help/articles/************-Guide-to-Slack-notifications</documentation>
        <element xsi:type="archimate:BusinessFunction" name="Sidebar notifications" id="id-ce4501284d114b0e8a4b8c9e0cae1cf0"/>
        <element xsi:type="archimate:BusinessFunction" name="Badge notifications" id="id-ed421e6f8338437684cd12e70d5b1be3">
          <documentation>When there’s unread activity in a conversation, the conversation name will appear bold in your sidebar. You’ll also see a numbered badge if someone mentions you.&#xD;
</documentation>
        </element>
        <element xsi:type="archimate:BusinessFunction" name="Email notification" id="id-10ceef034a404acb96f6b4728a8aad20">
          <documentation>- By default, you’ll receive email notifications when you join a Slack workspace and haven’t enabled mobile notifications&#xD;
- You can receive email notifications to alert you of mentions and DMs when you’re not active in Slack&#xD;
- These notifications are bundled and sent once every 15 minutes or once an hour, depending on your preferences&#xD;
</documentation>
        </element>
        <element xsi:type="archimate:BusinessFunction" name="Banner notifications" id="id-d6b81017667047728f98f6db999371b6"/>
      </folder>
      <folder name="Message features &amp; tools" id="id-2d9b7871e83d40eaa8863c5099c505fd">
        <element xsi:type="archimate:BusinessProcess" name="Notify a channel or workspace" id="id-9475497a952744b2a0c0b210157c6849">
          <documentation>https://slack.com/help/articles/202009646-Notify-a-channel-or-workspace&#xD;
&#xD;
- These mentions won't notify people when their notifications are paused, or when they're used in threads.&#xD;
- In channels with at least six members, Slack will ask you to confirm before you send a message with any of these mentions. Owners and admins can turn this warning off.&#xD;
- On the Enterprise Grid plan, these mentions work differently in channels. If there are 10,000 members or more, only owners and admins can use @channel and @here.&#xD;
&#xD;
</documentation>
        </element>
      </folder>
      <documentation>https://slack.com/help/categories/360000049043</documentation>
    </folder>
    <element xsi:type="archimate:Product" name="0 - Slack Doc" id="id-9b216cd2c71a47ce80ff7b49dd6811a7">
      <documentation>https://slack.com/help/categories/360000049043&#xD;
&#xD;
API&#xD;
https://api.slack.com/methods/chat.postMessage&#xD;
&#xD;
Glossary&#xD;
https://slack.com/help/articles/213817348-Slack-glossary&#xD;
&#xD;
Discord vs Slack – Gaming, Working or Both? (Our Team’s Feedback)&#xD;
https://www.chanty.com/blog/discord-vs-slack/</documentation>
    </element>
    <element xsi:type="archimate:BusinessObject" name="Channel" id="id-1efe7a2f8df04d57b80763b4e0ee6184"/>
    <element xsi:type="archimate:BusinessObject" name="Direct Messages" id="id-61d52c3a9089427d91e8cdabb8d58351"/>
    <element xsi:type="archimate:BusinessObject" name="Workspace" id="id-36c514dfe4d24acc8da28340baead2c5"/>
    <element xsi:type="archimate:BusinessActor" name="User" id="id-e87ef51c97a84b04829b5704099e0128"/>
  </folder>
  <folder name="Application" id="id-fb84c7b0c57c4448b3ef9b9ee4a77eff" type="application"/>
  <folder name="Technology &amp; Physical" id="id-bd811b2d18db448795a9b8d793a441f6" type="technology">
    <element xsi:type="archimate:Artifact" name="WS&lt;Workspace>" id="id-5f20d28a766844299fffb3bd2b8edeff"/>
    <element xsi:type="archimate:Artifact" name="WS&lt;Channel>" id="id-fa2110ae9f5e4e658dc1c7a074fad772"/>
    <element xsi:type="archimate:Artifact" name="WS&lt;sys.Profile>" id="id-3213f7ccbd754af89c6a5cbee011e9b2"/>
    <element xsi:type="archimate:Artifact" name="WDoc&lt;Message>" id="id-73f809828b7a4aafa51473e597a09f92"/>
    <element xsi:type="archimate:Artifact" name="WDoc&lt;Reply>" id="id-31a8008e3e5d4c4691f5c05a03451dd3"/>
    <element xsi:type="archimate:Artifact" name="Aggregate&lt;>" id="id-5dba60ae81374e378a3522dd9120f895"/>
    <element xsi:type="archimate:Artifact" name="CDoc&lt;WorkspaceChannel>" id="id-7ac70b72bf1e45dbae07a272be23c7dd"/>
    <element xsi:type="archimate:Artifact" name="CDoc&lt;sys.LinkedWorkspace>" id="id-4334d74f0a1f4986a7c1f43f2c53c65f"/>
    <element xsi:type="archimate:Artifact" name="CDoc&lt;sys.WorkspacePrincipal>" id="id-17b2413b81ae4c55bb0d9b3690e33b91"/>
    <element xsi:type="archimate:Artifact" name="CDoc&lt;ChannelWorkspace>" id="id-e7f4f3f89cef450da5dffd50f1bc444e"/>
  </folder>
  <folder name="Motivation" id="id-a833a96e0ad3412a8baa790031bc8633" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-191b4744d6c7469aaa47b14e519144ef" type="implementation_migration"/>
  <folder name="Other" id="id-abe98bec3dd5469a8623a6b941dae8b4" type="other"/>
  <folder name="Relations" id="id-24a78c01ab604f568356d72fab0ed643" type="relations">
    <element xsi:type="archimate:RealizationRelationship" id="id-1a2df9c4f51044e8bb15a697bc198faf" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-1efe7a2f8df04d57b80763b4e0ee6184"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-534aaafdab2c4f1f93668bb74eeaeb82" source="id-5f20d28a766844299fffb3bd2b8edeff" target="id-36c514dfe4d24acc8da28340baead2c5"/>
    <element xsi:type="archimate:AssociationRelationship" name=">&lt;" id="id-c2d0e799b5a3478ca10e439b250ba2b0" source="id-36c514dfe4d24acc8da28340baead2c5" target="id-1efe7a2f8df04d57b80763b4e0ee6184"/>
    <element xsi:type="archimate:AggregationRelationship" name="[]" id="id-0ea9c6f7a1da45a6bbd2baf96dfeca33" source="id-36c514dfe4d24acc8da28340baead2c5" target="id-61d52c3a9089427d91e8cdabb8d58351"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-ffebc02f3c4d41ce82460a545ba425a4" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-61d52c3a9089427d91e8cdabb8d58351"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-e8802764b6494effb8505f7b293292f2" source="id-e87ef51c97a84b04829b5704099e0128" target="id-3213f7ccbd754af89c6a5cbee011e9b2"/>
    <element xsi:type="archimate:AggregationRelationship" name="[]" id="id-f7779fea242641e9a80a2b2f0f5ca96e" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-7ac70b72bf1e45dbae07a272be23c7dd"/>
    <element xsi:type="archimate:AggregationRelationship" name="[]" id="id-e280d1775cb24004a9a69c404c670681" source="id-5f20d28a766844299fffb3bd2b8edeff" target="id-4334d74f0a1f4986a7c1f43f2c53c65f"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-74bab4731c214cf38a617d219342d83a" source="id-3213f7ccbd754af89c6a5cbee011e9b2" target="id-4334d74f0a1f4986a7c1f43f2c53c65f"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-b341b0b3c8b14571a234911c084df151" source="id-5f20d28a766844299fffb3bd2b8edeff" target="id-7ac70b72bf1e45dbae07a272be23c7dd"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-c473c02e79924e9faaa2c343f2256d60" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-73f809828b7a4aafa51473e597a09f92"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-6f46ccd4622542389a0014ec6d5142bb" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-31a8008e3e5d4c4691f5c05a03451dd3"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-f816eef9d2794ad4bb19bea4de044631" source="id-7ac70b72bf1e45dbae07a272be23c7dd" target="id-9de7ca305c8b43518b0bc486a29b8762"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-019000baf6e7452389af7f08618805a8" source="id-a8f2fb04da7f451d80191133b0cb190b" target="id-7ac70b72bf1e45dbae07a272be23c7dd"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-8c5b983921df4a52a246cb5cea4c11e8" source="id-1ebf0b62da794785b29537b713f21271" target="id-4334d74f0a1f4986a7c1f43f2c53c65f"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-3f808e1e954d4db498c8ad5b5a2ef7b9" source="id-e3f3b9bd8a514c17900191bc112701c4" target="id-4334d74f0a1f4986a7c1f43f2c53c65f"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-e7488fda24c34f80ba9d995a2ff2d29f" source="id-5f20d28a766844299fffb3bd2b8edeff" target="id-17b2413b81ae4c55bb0d9b3690e33b91"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-f4d7162b80d84ebbb5c0ab1b1627ac5c" source="id-e3f3b9bd8a514c17900191bc112701c4" target="id-17b2413b81ae4c55bb0d9b3690e33b91"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-469f191fecc2474e9f8b10d8fa47837f" source="id-1ebf0b62da794785b29537b713f21271" target="id-17b2413b81ae4c55bb0d9b3690e33b91"/>
    <element xsi:type="archimate:SpecializationRelationship" id="id-57196f9d906545a1a4c3e9139135a787" source="id-9a05bd515587459e84c46f77b800c9bd" target="id-1ebf0b62da794785b29537b713f21271"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-d6a9154a0bf84eeebeadc3ee3609dfc8" source="id-a8f2fb04da7f451d80191133b0cb190b" target="id-fa2110ae9f5e4e658dc1c7a074fad772"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-490b8ebbcd4d4e3cbb9dc3249ae4087c" source="id-9de7ca305c8b43518b0bc486a29b8762" target="id-e7f4f3f89cef450da5dffd50f1bc444e"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-3589528b85c74738927d9570fc8a37d4" source="id-327d5d6fbf16416fb09fe6edabc42a2f" target="id-7ac70b72bf1e45dbae07a272be23c7dd"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-01544ef858e44c4a8372d06bd2a87b38" source="id-327d5d6fbf16416fb09fe6edabc42a2f" target="id-e7f4f3f89cef450da5dffd50f1bc444e"/>
    <element xsi:type="archimate:CompositionRelationship" name="[]" id="id-8fc64aeb37b9470592b984e3b9581e5d" source="id-fa2110ae9f5e4e658dc1c7a074fad772" target="id-e7f4f3f89cef450da5dffd50f1bc444e"/>
  </folder>
  <folder name="Views" id="id-6484d2fd65e5426fa267661775ba3d71" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Database" id="id-f48745f05248417599102521060acbf4">
      <child xsi:type="archimate:DiagramObject" id="id-e866f6ae67394b9580ec203dec3b37a8" archimateElement="id-5f20d28a766844299fffb3bd2b8edeff">
        <bounds x="756" y="288" width="265" height="193"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-f51c54c160ca45b98d4be51930bae91c" source="id-e866f6ae67394b9580ec203dec3b37a8" target="id-50f16dc58c8d4ca5ae797c3dab3a3f75" archimateRelationship="id-534aaafdab2c4f1f93668bb74eeaeb82"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-bb020d7f2c274f1589ece58912339170" source="id-e866f6ae67394b9580ec203dec3b37a8" target="id-a541cf4bf25845ebaa25e64743ef6b2f" archimateRelationship="id-e280d1775cb24004a9a69c404c670681"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-fd205b428c55498ea13ff8e81a582701" source="id-e866f6ae67394b9580ec203dec3b37a8" target="id-73982ee7692040d9bfbc54d27bc3eac5" archimateRelationship="id-b341b0b3c8b14571a234911c084df151"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-aa42dd22abc1455aa363f09e6787fcc8" archimateElement="id-fa2110ae9f5e4e658dc1c7a074fad772">
        <bounds x="264" y="550" width="409" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-369b7b1159f14b4ea5ca054929382e07" source="id-aa42dd22abc1455aa363f09e6787fcc8" target="id-8e6d0a85809440df99e3bfe726f7cb83" archimateRelationship="id-1a2df9c4f51044e8bb15a697bc198faf"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c3f0cb166fdb453e84c4dfd819e511f7" source="id-aa42dd22abc1455aa363f09e6787fcc8" target="id-2721c0de9b8f44a9a22d301964dea189" archimateRelationship="id-ffebc02f3c4d41ce82460a545ba425a4"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-378db72d84724efa98fbf98411fc2ee5" source="id-aa42dd22abc1455aa363f09e6787fcc8" target="id-73982ee7692040d9bfbc54d27bc3eac5" archimateRelationship="id-f7779fea242641e9a80a2b2f0f5ca96e"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-8d1ef7b92c544768b06fc628d243e220" source="id-aa42dd22abc1455aa363f09e6787fcc8" target="id-dd906bac0fa34be7bc259349d370bd79" archimateRelationship="id-c473c02e79924e9faaa2c343f2256d60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-b748e3de543f4ffbb92fa8f660c9f60a" source="id-aa42dd22abc1455aa363f09e6787fcc8" target="id-7cd20ec8c4c4439ba1e123e5905f2223" archimateRelationship="id-6f46ccd4622542389a0014ec6d5142bb"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-8e6d0a85809440df99e3bfe726f7cb83" targetConnections="id-369b7b1159f14b4ea5ca054929382e07 id-e2b4f59ad971441f8aac1db9b04d72a1" archimateElement="id-1efe7a2f8df04d57b80763b4e0ee6184">
        <bounds x="264" y="406" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-50f16dc58c8d4ca5ae797c3dab3a3f75" targetConnections="id-f51c54c160ca45b98d4be51930bae91c" archimateElement="id-36c514dfe4d24acc8da28340baead2c5">
        <bounds x="264" y="288" width="385" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-e2b4f59ad971441f8aac1db9b04d72a1" source="id-50f16dc58c8d4ca5ae797c3dab3a3f75" target="id-8e6d0a85809440df99e3bfe726f7cb83" archimateRelationship="id-c2d0e799b5a3478ca10e439b250ba2b0"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-9de38a5c733141f4bd8dad117baa3cb1" source="id-50f16dc58c8d4ca5ae797c3dab3a3f75" target="id-2721c0de9b8f44a9a22d301964dea189" archimateRelationship="id-0ea9c6f7a1da45a6bbd2baf96dfeca33"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-2721c0de9b8f44a9a22d301964dea189" targetConnections="id-9de38a5c733141f4bd8dad117baa3cb1 id-c3f0cb166fdb453e84c4dfd819e511f7" archimateElement="id-61d52c3a9089427d91e8cdabb8d58351">
        <bounds x="529" y="406" width="120" height="55"/>
        <feature name="imageSource" value="1"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-ada88b5810e5484aa7acbea7bb4de0cd" archimateElement="id-e87ef51c97a84b04829b5704099e0128">
        <bounds x="529" y="48" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-bb330d5f07af435693af58e72d68817f" source="id-ada88b5810e5484aa7acbea7bb4de0cd" target="id-332a61b5db5c45cda1d40c652544d70e" archimateRelationship="id-e8802764b6494effb8505f7b293292f2"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-332a61b5db5c45cda1d40c652544d70e" targetConnections="id-bb330d5f07af435693af58e72d68817f" archimateElement="id-3213f7ccbd754af89c6a5cbee011e9b2">
        <bounds x="756" y="48" width="265" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-679c0c4d8c394056937e8206ca521149" source="id-332a61b5db5c45cda1d40c652544d70e" target="id-a541cf4bf25845ebaa25e64743ef6b2f" archimateRelationship="id-74bab4731c214cf38a617d219342d83a"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-dd906bac0fa34be7bc259349d370bd79" targetConnections="id-8e3bd6f46717410ea62c668726b2fed7 id-8d1ef7b92c544768b06fc628d243e220" archimateElement="id-73f809828b7a4aafa51473e597a09f92">
        <bounds x="264" y="672" width="169" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-7cd20ec8c4c4439ba1e123e5905f2223" targetConnections="id-b748e3de543f4ffbb92fa8f660c9f60a" archimateElement="id-31a8008e3e5d4c4691f5c05a03451dd3">
        <bounds x="504" y="672" width="169" height="55"/>
      </child>
      <child xsi:type="archimate:Note" id="id-39258b2c7a65417b9692923bdd80209f" textAlignment="1">
        <bounds x="48" y="675" width="185" height="49"/>
        <sourceConnection id="id-8e3bd6f46717410ea62c668726b2fed7" source="id-39258b2c7a65417b9692923bdd80209f" target="id-dd906bac0fa34be7bc259349d370bd79"/>
        <content>-N10n. Unread channels</content>
      </child>
      <child xsi:type="archimate:Note" id="id-e4f2f8bb7cc64d9c970207fb1d3e1d28" textAlignment="1">
        <bounds x="264" y="780" width="169" height="97"/>
        <content>Aggregate: Кластер связанных объектов, которые рассматриваются как единое целое с целью изменения данных.</content>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-73982ee7692040d9bfbc54d27bc3eac5" targetConnections="id-378db72d84724efa98fbf98411fc2ee5 id-fd205b428c55498ea13ff8e81a582701" archimateElement="id-7ac70b72bf1e45dbae07a272be23c7dd">
        <bounds x="756" y="552" width="265" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-a541cf4bf25845ebaa25e64743ef6b2f" targetConnections="id-bb020d7f2c274f1589ece58912339170 id-679c0c4d8c394056937e8206ca521149" archimateElement="id-4334d74f0a1f4986a7c1f43f2c53c65f">
        <bounds x="756" y="156" width="265" height="55"/>
      </child>
      <documentation>https://docs.axoniq.io/reference-guide/architecture-overview/ddd-cqrs-concepts&#xD;
</documentation>
    </element>
    <element xsi:type="archimate:ArchimateDiagramModel" name="Channels" id="id-92e83f4dcd124cd3b1417476854195cc">
      <child xsi:type="archimate:DiagramObject" id="id-fa52d23076e041b2b1fffaeb0caf6119" targetConnections="id-36596224a41a442294750fbe4bfc4539" archimateElement="id-9de7ca305c8b43518b0bc486a29b8762">
        <bounds x="216" y="768" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-43e8f2abb3564d8b9f776addecd7047a" source="id-fa52d23076e041b2b1fffaeb0caf6119" target="id-190c555cbab54b28b8d7be4523876f1f" archimateRelationship="id-490b8ebbcd4d4e3cbb9dc3249ae4087c"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-6e3a70578a2248cca6d188b4b7ccf997" archimateElement="id-9a05bd515587459e84c46f77b800c9bd">
        <bounds x="204" y="144" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-23de206a23a14a7bbabf53f72bc450d9" source="id-6e3a70578a2248cca6d188b4b7ccf997" target="id-aea1304f8ac844f795993eec4b5bfe93" archimateRelationship="id-57196f9d906545a1a4c3e9139135a787"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-6039fbad17944fad8a73830ab64aab13" archimateElement="id-a8f2fb04da7f451d80191133b0cb190b">
        <bounds x="216" y="648" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-9911940fbf18408eb1f2b251d2fb1337" source="id-6039fbad17944fad8a73830ab64aab13" target="id-2b294b5e13fb405eb903801f8c16448a" archimateRelationship="id-019000baf6e7452389af7f08618805a8"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-19e82d9e06a74c64b75961725530cad3" source="id-6039fbad17944fad8a73830ab64aab13" target="id-020ef13c4b8c4784b20b930a57ced627" archimateRelationship="id-d6a9154a0bf84eeebeadc3ee3609dfc8"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-aea1304f8ac844f795993eec4b5bfe93" targetConnections="id-23de206a23a14a7bbabf53f72bc450d9" archimateElement="id-1ebf0b62da794785b29537b713f21271">
        <bounds x="204" y="252" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-5b2ddcdfa248454a8df1df9f122c4cdb" source="id-aea1304f8ac844f795993eec4b5bfe93" target="id-ecf159b2c8a5400ea3bdba9c289c56cc" archimateRelationship="id-8c5b983921df4a52a246cb5cea4c11e8"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-9dff99c1fd0c4794a41ca8ef161b5fab" source="id-aea1304f8ac844f795993eec4b5bfe93" target="id-5788cb08efde40829c973da2169b0e40" archimateRelationship="id-469f191fecc2474e9f8b10d8fa47837f"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-37a0c9ef92754245a503c148b7877c61" archimateElement="id-e3f3b9bd8a514c17900191bc112701c4">
        <bounds x="204" y="336" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-bc5ff72803e843ff9416bbb86ee72037" source="id-37a0c9ef92754245a503c148b7877c61" target="id-ecf159b2c8a5400ea3bdba9c289c56cc" archimateRelationship="id-3f808e1e954d4db498c8ad5b5a2ef7b9"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-ccf3e03d344b40ae9a7e8949c404fd9d" source="id-37a0c9ef92754245a503c148b7877c61" target="id-5788cb08efde40829c973da2169b0e40" archimateRelationship="id-f4d7162b80d84ebbb5c0ab1b1627ac5c"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-6900064f48124882a0b27b9f8fc040d1" archimateElement="id-5f20d28a766844299fffb3bd2b8edeff">
        <bounds x="624" y="480" width="265" height="133"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-7e5d2486570147898a6f6abfc1692393" source="id-6900064f48124882a0b27b9f8fc040d1" target="id-2b294b5e13fb405eb903801f8c16448a" archimateRelationship="id-b341b0b3c8b14571a234911c084df151"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-0b44cc411f8e4c46a1e047bd5f4b1cfe" source="id-6900064f48124882a0b27b9f8fc040d1" target="id-5788cb08efde40829c973da2169b0e40" archimateRelationship="id-e7488fda24c34f80ba9d995a2ff2d29f"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-2b294b5e13fb405eb903801f8c16448a" targetConnections="id-7e5d2486570147898a6f6abfc1692393 id-9911940fbf18408eb1f2b251d2fb1337 id-4b5ba9d3f6424d878295a6c44ce79665 id-0191b02719774276a92943c7b9bbdadf" archimateElement="id-7ac70b72bf1e45dbae07a272be23c7dd">
        <bounds x="624" y="684" width="265" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-36596224a41a442294750fbe4bfc4539" source="id-2b294b5e13fb405eb903801f8c16448a" target="id-fa52d23076e041b2b1fffaeb0caf6119" archimateRelationship="id-f816eef9d2794ad4bb19bea4de044631"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-814b2a428887416aacbe72bffe3198a6" archimateElement="id-3213f7ccbd754af89c6a5cbee011e9b2">
        <bounds x="624" y="132" width="265" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-9e4a58fde5ec4dc7b4a977e44de237b8" source="id-814b2a428887416aacbe72bffe3198a6" target="id-ecf159b2c8a5400ea3bdba9c289c56cc" archimateRelationship="id-74bab4731c214cf38a617d219342d83a"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-ecf159b2c8a5400ea3bdba9c289c56cc" targetConnections="id-9e4a58fde5ec4dc7b4a977e44de237b8 id-5b2ddcdfa248454a8df1df9f122c4cdb id-bc5ff72803e843ff9416bbb86ee72037" archimateElement="id-4334d74f0a1f4986a7c1f43f2c53c65f">
        <bounds x="624" y="240" width="265" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-5788cb08efde40829c973da2169b0e40" targetConnections="id-0b44cc411f8e4c46a1e047bd5f4b1cfe id-ccf3e03d344b40ae9a7e8949c404fd9d id-9dff99c1fd0c4794a41ca8ef161b5fab" archimateElement="id-17b2413b81ae4c55bb0d9b3690e33b91">
        <bounds x="624" y="336" width="265" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-020ef13c4b8c4784b20b930a57ced627" targetConnections="id-19e82d9e06a74c64b75961725530cad3" archimateElement="id-fa2110ae9f5e4e658dc1c7a074fad772">
        <bounds x="624" y="804" width="265" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-4b5ba9d3f6424d878295a6c44ce79665" source="id-020ef13c4b8c4784b20b930a57ced627" target="id-2b294b5e13fb405eb903801f8c16448a" archimateRelationship="id-f7779fea242641e9a80a2b2f0f5ca96e"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-d548b591401d410c8be5e71d36bcd352" source="id-020ef13c4b8c4784b20b930a57ced627" target="id-190c555cbab54b28b8d7be4523876f1f" archimateRelationship="id-8fc64aeb37b9470592b984e3b9581e5d"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-190c555cbab54b28b8d7be4523876f1f" targetConnections="id-43e8f2abb3564d8b9f776addecd7047a id-0c0b0657225e4897bcef25cfb76ee429 id-d548b591401d410c8be5e71d36bcd352" archimateElement="id-e7f4f3f89cef450da5dffd50f1bc444e">
        <bounds x="624" y="912" width="265" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-ec32e4fa0606412383e2b4baf9944835" archimateElement="id-327d5d6fbf16416fb09fe6edabc42a2f">
        <bounds x="216" y="876" width="169" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-0191b02719774276a92943c7b9bbdadf" source="id-ec32e4fa0606412383e2b4baf9944835" target="id-2b294b5e13fb405eb903801f8c16448a" archimateRelationship="id-3589528b85c74738927d9570fc8a37d4"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-0c0b0657225e4897bcef25cfb76ee429" source="id-ec32e4fa0606412383e2b4baf9944835" target="id-190c555cbab54b28b8d7be4523876f1f" archimateRelationship="id-01544ef858e44c4a8372d06bd2a87b38"/>
      </child>
    </element>
    <element xsi:type="archimate:ArchimateDiagramModel" name="Messages" id="id-5b2769805160482e899eaab263616885"/>
    <element xsi:type="archimate:SketchModel" name="Channel" id="id-1114f57b4c654590a0b1df41537fc78d" background="0">
      <child xsi:type="archimate:SketchModelSticky" id="id-7ef36adbc5ba4d748ad6a2fb81d0af42" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="610" y="360" width="253" height="85"/>
        <content>Message&#xD;
- firstReplyIdx&#xD;
- firstRmojiIdx&#xD;
- emojiCounts []{int, int}</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-0bc2700f01554fe0a066143a57f9b33d" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#ffffb5">
        <bounds x="120" y="612" width="421" height="205"/>
        <content>Messages operations&#xD;
&#xD;
- Read messages frame&#xD;
- Add emoji&#xD;
&#xD;
- Read replies frame&#xD;
</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-41fbeb73e70e4a8893f6598bd3d2a449" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="650" y="792" width="213" height="110"/>
        <content>WDoc&#xD;
&#xD;
- collection size: a lot&#xD;
- editable: yes&#xD;
- lifetime: short&#xD;
- frame: by time</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-47dbb91798dc4b3bb9da38fbab644a83" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#ffffb5">
        <bounds x="132" y="960" width="253" height="37"/>
        <content>Slack: Read replies list area</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-450ca46e1d48468ba3b987bf0d103454" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#ffffb5">
        <bounds x="132" y="1104" width="253" height="37"/>
        <content>Slack: Read messages list area</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-5db31ca2563945a081ccea2cc2ffa91a" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#ffffb5">
        <bounds x="132" y="1020" width="253" height="37"/>
        <content>Slack: Add emoji</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-c6a6d85674bd4dff85d5771a5c339fa9" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="650" y="660" width="253" height="36"/>
        <content>replies WDoc2&lt;Message>[]</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-1e7d6da3d5914a9b8dc58f624006cd6d" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="648" y="552" width="217" height="61"/>
        <content>WDoc2&lt;Message>&#xD;
- Thread []</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-a866c9e5d8014c1c8b7bf2c434775004" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#b5ffff">
        <bounds x="120" y="468" width="397" height="85"/>
        <content>AbstractMessage: struct&#xD;
- Reactions LargeMap&lt;User, Emoji>&#xD;
- ReactionCounts map[???]int&#xD;
</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-511f0f6826814b55bf5c8316642b2046" name="Sticky" font="1|Segoe UI|12.0|0|WINDOWS|1|-16|0|0|0|400|0|0|0|-52|3|2|1|34|Segoe UI" lineColor="#5c5c5c" textAlignment="1" fillColor="#b5ffff">
        <bounds x="144" y="24" width="397" height="373"/>
        <content>Workspace &#xD;
- Messages List&lt;Large, RootMessage>&#xD;
&#xD;
List &#xD;
- item has index&#xD;
- Items can be read by frames around index (with authz)&#xD;
&#xD;
RootMessage extends AbstractMessage&#xD;
- Thread InnerList&lt;Reply>&#xD;
&#xD;
Reply extends AbstractMessage&#xD;
&#xD;
AbstractMessage: struct&#xD;
- Reactions LargeMap&lt;User, Emoji>&#xD;
- ReactionCounts map[???]int&#xD;
</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-74ba6dec2f9048c1b12a65855bf022f3" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="972" y="60" width="253" height="193"/>
        <content>Channel Workspace&#xD;
- P_Messages List&lt;RootMessage>&#xD;
- P_Thread List&lt;Reply>&#xD;
- P_EmojiThread List&lt;???>&#xD;
- P_EmojiIdx Index{???}</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-a78eb62b154948678a05813ae0591c13" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="929" y="408" width="296" height="241"/>
        <content>P_LinkedListBuckets&#xD;
- []LinkedListBucket&#xD;
&#xD;
LinkedListBucket {&#xD;
- owner RefToOwnerr //for indexing&#xD;
- prevBucketIdx uint32&#xD;
- nextBucketIdx uint32&#xD;
&#xD;
// 100 is more effective for reading&#xD;
// less effective for even distribution&#xD;
- bucket [10]P_Row&#xD;
}&#xD;
&#xD;
// No ref to outer element???</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-ac49de8263f046848df3f8d162f09556" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="624" y="60" width="253" height="85"/>
        <content>Workspace&#xD;
- P_List&#xD;
- P_LinkedListBuckets&#xD;
</content>
      </child>
      <child xsi:type="archimate:SketchModelSticky" id="id-687f26aee32143139a9745da919a3949" name="Sticky" lineColor="#5c5c5c" textAlignment="1" fillColor="#c9e7b7">
        <bounds x="624" y="168" width="253" height="85"/>
        <content>P_List&#xD;
- QNameID&#xD;
- qnameid_related_idx</content>
      </child>
    </element>
    <element xsi:type="archimate:ArchimateDiagramModel" name="Software + Physical" id="id-6f2b9a48368d4196b156ed406ebafe27"/>
  </folder>
  <profile name="Specialization 1" id="id-3194ebd31afc4e32af0182acf4f435f1" conceptType="BusinessProcess"/>
</archimate:model>
