// Code generated by "stringer -type=RateLimitKind"; DO NOT EDIT.

package istructs

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[RateLimitKind_byApp-0]
	_ = x[RateLimitKind_byWorkspace-1]
	_ = x[RateLimitKind_byID-2]
	_ = x[RateLimitKind_FakeLast-3]
}

const _RateLimitKind_name = "RateLimitKind_byAppRateLimitKind_byWorkspaceRateLimitKind_byIDRateLimitKind_FakeLast"

var _RateLimitKind_index = [...]uint8{0, 19, 44, 62, 84}

func (i RateLimitKind) String() string {
	if i >= RateLimitKind(len(_RateLimitKind_index)-1) {
		return "RateLimitKind(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _RateLimitKind_name[_RateLimitKind_index[i]:_RateLimitKind_index[i+1]]
}
