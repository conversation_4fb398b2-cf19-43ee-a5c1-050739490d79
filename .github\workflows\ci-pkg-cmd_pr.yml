name: CI pkg-cmd PR

on: 
  pull_request_target:
    paths-ignore:
      - 'pkg/istorage/**'

jobs:
  call-workflow-ci-pkg:
    if: github.repository == 'voedger/voedger'
    uses: untillpro/ci-action/.github/workflows/ci_reuse_go_pr.yml@master
    with:
      test_folder: "pkg"
      ignore_copyright: "cmd/voedger/sys.monitor/site.main"
      ignore_bp3: "true"
      short_test: "true"
      ignore_build: "true"
      running_workflow: "CI pkg-cmd PR"
      go_race: "false"
      test_subfolders: "true" 
    secrets:
      reporeading_token: ${{ secrets.REPOREADING_TOKEN }}
      codecov_token: ""
      personal_token: ${{ secrets.PERSONAL_TOKEN }}
  auto-merge-pr:
    needs: call-workflow-ci-pkg
    uses: ./.github/workflows/merge.yml
    secrets:
      personaltoken: ${{ secrets.REPOREADING_TOKEN }}
