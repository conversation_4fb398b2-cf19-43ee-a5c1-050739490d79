/*
 * Copyright (c) 2021-present Sigma-Soft, Ltd.
 * <AUTHOR>
 *
 */

package itokensjwt

const (
	numberOfParts       = 3
	SecretKeyLength     = 64
	hashLength          = 32
	errorVerifyAudience = "error verify token, this token have %s audience and was intended for principal type %s %w"
	SecretKeyJWTName    = "secretKeyJWT"
)

var SecretKeyExample = SecretKeyType{
	0x7e, 0x7f, 0x54, 0xfc, 0xbd, 0x1d, 0x6d, 0xe5, 0x6b, 0xc1, 0x01, 0xe7, 0x5c, 0xed, 0x13, 0x5d,
	0xf3, 0xa3, 0x57, 0x1d, 0x51, 0xae, 0xbd, 0x12, 0xf7, 0xc4, 0xda, 0xd4, 0xee, 0x62, 0xff, 0xe0,
	0x43, 0xc6, 0xcc, 0xfb, 0x6e, 0x39, 0x91, 0xd2, 0xa6, 0x1f, 0x7c, 0x87, 0x8b, 0xa0, 0x4d, 0xe6,
	0x08, 0x4a, 0x28, 0xc9, 0x8d, 0xde, 0x39, 0xf9, 0xca, 0xeb, 0x75, 0x52, 0x7e, 0x58, 0x71, 0x31,
}
