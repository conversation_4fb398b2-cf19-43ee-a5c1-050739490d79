global:
  scrape_interval:     30s # By default, scrape targets every 1 minute.
  evaluation_interval: 30s # By default, evaluate rules every 1 minute.
  external_labels:
     node: ce  # mon1 for example. Use label as node.label.type in swarm
  #scrape_timeout:     10s # By default, a scrape request times out in 10 seconds.
  
# Alertmanager configuration
alerting:
  alert_relabel_configs:
    - source_labels: [node]
      regex: (.+)\d+
      target_label: node
  alertmanagers:
    - static_configs:
      - targets:
        - ${VOEDGER_CE_NODE}:9093 # during deploy replace with real ip address of alert manager

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert.rules"

  # Configuring Prometheus to monitor itself - https://prometheus.io/docs/prometheus/latest/getting_started/
scrape_configs:
  - job_name: 'prometheus'
    # Override the global values and scrape targets for this job every 10 seconds.
    scrape_interval: 10s
    static_configs:
      # Execute query expressions prometheus_abc_xyz
      - targets: 
        - ${VOEDGER_CE_NODE}:9090

  - job_name: 'node-exporter'
    scrape_interval: 10s
    static_configs:
      # node-exporter:9100 where node-exporter is service name in docker-compose.yml 
      # Execute non-prometheus_abc_xyz query expressions, e.g., node_load1 etc.
      - targets: 
        - ${VOEDGER_CE_NODE}:9100

  - job_name: 'scylla-cluster'
    scrape_interval: 10s
    static_configs:
      # Monitor scylla nodes with embedded scylla exporter
      - targets: 
        - ${VOEDGER_CE_NODE}:9180

  - job_name: 'cadvisor'
    scrape_interval: 10s
    static_configs:
      # Monitor swarm nodes and services
      - targets: 
        - ${VOEDGER_CE_NODE}:8080

  - job_name: 'voedger'
    scrape_interval: 10s
    static_configs:
      - targets: 
        - ${VOEDGER_CE_NODE}:8000
