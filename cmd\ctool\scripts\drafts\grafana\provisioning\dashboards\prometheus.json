{"annotations": {"list": [{"$$hashKey": "object:698", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 7, "links": [{"icon": "info", "tags": [], "targetBlank": true, "title": "<PERSON>ana <PERSON>s", "tooltip": "", "type": "link", "url": "http://docs.grafana.org/"}, {"icon": "info", "tags": [], "targetBlank": true, "title": "Prometheus Docs", "type": "link", "url": "http://prometheus.io/docs/introduction/overview/"}], "liveNow": false, "panels": [{"aliasColors": {"prometheus": "#C15C17", "{instance=\"localhost:9090\",job=\"prometheus\"}": "#CCA300"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(prometheus_tsdb_head_samples_appended_total{job=\"prometheus\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "samples", "metric": "", "refId": "A", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Samples appended", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(5, max(scrape_duration_seconds) by (job))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Scrape duration", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_resident_memory_bytes{job=\"prometheus\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "p8s process resident memory", "refId": "D", "step": 20}, {"expr": "process_virtual_memory_bytes{job=\"prometheus\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "virtual memory", "refId": "C", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Memory profile", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "None"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.1}, {"color": "rgba(245, 54, 54, 0.9)", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 37, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["max"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "prometheus_tsdb_wal_truncations_failed_total{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 60}], "title": "WAL corruptions", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 5}, "hiddenSeries": false, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(prometheus_tsdb_head_active_appenders{job=\"prometheus\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "active_appenders", "metric": "", "refId": "A", "step": 20}, {"expr": "sum(process_open_fds{job=\"prometheus\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "open_fds", "refId": "B", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Active appenders", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"prometheus": "#F9BA8F", "{instance=\"localhost:9090\",interval=\"5s\",job=\"prometheus\"}": "#F9BA8F"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 5}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_blocks_loaded{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "blocks", "refId": "A", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Blocks loaded", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 5}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_chunks{job=\"prometheus\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "chunks", "refId": "A", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Head chunks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "bytes", "label": "", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 5}, "hiddenSeries": false, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "duration-p99", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_gc_duration_seconds{job=\"prometheus\",quantile=\"0.99\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "duration-p99", "refId": "A", "step": 20}, {"expr": "irate(prometheus_tsdb_head_gc_duration_seconds_count{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "collections", "refId": "B", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Head block gc activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "s", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 10}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "duration-p99", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(prometheus_tsdb_compaction_duration_bucket{job=\"prometheus\"}[5m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "duration-{{p99}}", "refId": "A", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "compactions", "refId": "B", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_failed_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "failed", "refId": "C", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_triggered_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "triggered", "refId": "D", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Compaction activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "s", "label": "", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 10}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_reloads_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "reloads", "refId": "A", "step": 20}, {"expr": "rate(prometheus_tsdb_reloads_failures_total{job=\"prometheus\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "failures", "refId": "B", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Reload count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 10}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_engine_query_duration_seconds{job=\"prometheus\", quantile=\"0.99\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{slice}}_p99", "refId": "A", "step": 20}], "thresholds": [], "timeRegions": [], "title": "Query durations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(prometheus_rule_group_duration_seconds{job=\"prometheus\"}) by (quantile)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{quantile}}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Rule group eval duration", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 39, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(prometheus_rule_group_iterations_missed_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "missed", "refId": "B", "step": 10}, {"expr": "rate(prometheus_rule_group_iterations_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "iterations", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Rule group eval activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "1m", "revision": "1.0", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "UTC", "title": "Prometheus stats", "uid": "mGFfYSRiz", "version": 2, "weekStart": ""}