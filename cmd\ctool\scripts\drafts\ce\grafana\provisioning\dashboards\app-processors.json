{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 8, "iteration": 1715790575125, "links": [], "liveNow": false, "panels": [{"gridPos": {"h": 2, "w": 8, "x": 0, "y": 0}, "id": 104, "options": {"content": "<div style=\"text-align:center; font-size:200%\">\r\nCommand processors\r\n<div>", "mode": "html"}, "pluginVersion": "8.3.4", "transparent": true, "type": "text"}, {"gridPos": {"h": 2, "w": 8, "x": 8, "y": 0}, "id": 105, "options": {"content": "<div style=\"text-align:center; font-size:200%\">\r\nQuery processors\r\n<div>", "mode": "html"}, "pluginVersion": "8.3.4", "transparent": true, "type": "text"}, {"gridPos": {"h": 2, "w": 8, "x": 16, "y": 0}, "id": 106, "options": {"content": "<div style=\"text-align:center; font-size:200%\">\r\nActualizers\r\n<div>", "mode": "html"}, "pluginVersion": "8.3.4", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 2}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": " increase(voedger_cp_commands_total{app=~\"$app\"}[$aggregation_interval]) ", "instant": false, "interval": "", "legendFormat": "{{app}}", "range": true, "refId": "A"}], "title": "Commands, per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 2}, "id": 45, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "exemplar": true, "expr": "increase(voedger_qp_queries_total{app=~\"$app\"}[$aggregation_interval])", "interval": "", "legendFormat": "{{app}}", "range": true, "refId": "A"}], "title": "Queries, per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "minWidth": 80}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": [{"matcher": {"id": "byType", "options": "time"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "color"}, {"id": "custom.displayMode", "value": "color-text"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 2}, "id": 65, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": false, "sortBy": []}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": false, "expr": "voedger_projectors_in_error{app=~\"$app\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "$app", "refId": "A"}], "title": "Projectors in error state (now)", "type": "table"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 8}, "id": 71, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "increase(voedger_cp_commands_seconds{app=~\"$app\"}[$aggregation_interval])/increase(voedger_cp_commands_total{app=~\"$app\"}[$aggregation_interval])", "interval": "", "legendFormat": "{{app}}", "range": true, "refId": "C"}], "title": "Latency, avg per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 8}, "id": 70, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "increase(voedger_qp_queries_seconds{app=~\"$app\"}[$aggregation_interval])/increase(voedger_qp_queries_total{app=~\"$app\"}[$aggregation_interval]) > 0 or on() increase(voedger_qp_queries_total{app=~\"$app\"}[$aggregation_interval])", "interval": "", "legendFormat": "{{app}}", "range": true, "refId": "C"}], "title": "Latency, avg per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 8}, "id": 67, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": "Prometheus", "exemplar": true, "expr": "sum by (app) (voedger_projectors_in_error{app=~\"$app\"})", "interval": "", "legendFormat": "{{app}}", "refId": "A"}], "title": "Projectors in error state (history)", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 14}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": "Prometheus", "exemplar": true, "expr": "sum by (app) (increase(voedger_cp_errors_total{app=~\"$app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "{{app}}", "refId": "A"}], "title": "Errors, per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 14}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "exemplar": true, "expr": "increase(voedger_qp_errors_total{app=~\"$app\"}[$aggregation_interval])", "interval": "", "legendFormat": "{{app}}", "refId": "A"}], "title": "Errors, per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "minWidth": 80}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"index": 0, "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "custom.minWidth", "value": 80}, {"id": "displayName", "value": "Total commands"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Avg latency"}, {"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "Errors"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "text", "value": null}, {"color": "red", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vvm"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "app"}, "properties": [{"id": "displayName", "value": "App"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 20}, "id": 43, "links": [], "maxDataPoints": 100, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": "Prometheus", "exemplar": false, "expr": "sum by (app) (increase(voedger_cp_commands_total{app=~\"$app\"}[$__range]))", "format": "table", "instant": true, "interval": "", "legendFormat": "Total commands", "refId": "A"}, {"datasource": "Prometheus", "exemplar": false, "expr": "avg by (app) (increase(voedger_cp_commands_seconds{app=~\"$app\"}[$__range]))/avg by (app) (increase(voedger_cp_commands_total{app=~\"$app\"}[$__range]))>0 OR on() increase(voedger_cp_commands_total{app=~\"$app\"}[$__range])", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Avg Latency", "refId": "B"}, {"datasource": "Prometheus", "exemplar": false, "expr": "sum by (app) (increase(voedger_cp_errors_total{app=~\"$app\"}[$__range]))", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}], "title": "Overall CP", "transformations": [{"id": "merge", "options": {}}], "type": "table"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "minWidth": 80}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"index": 0, "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "custom.minWidth", "value": 80}, {"id": "displayName", "value": "Total queries"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Avg latency"}, {"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Errors"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "text", "value": null}, {"color": "red", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vvm"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "app"}, "properties": [{"id": "displayName", "value": "App"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 20}, "id": 83, "links": [], "maxDataPoints": 100, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": "Prometheus", "exemplar": false, "expr": "sum by (app) (increase(voedger_qp_queries_total{app=~\"$app\"}[$__range]))", "format": "table", "instant": true, "interval": "", "legendFormat": "Value", "refId": "A"}, {"datasource": "Prometheus", "exemplar": false, "expr": "avg by (app) (increase(voedger_qp_queries_seconds{app=~\"$app\"}[$__range]))/avg by (app) (increase(voedger_qp_queries_total{app=~\"$app\"}[$__range]))>0 OR on() increase(voedger_qp_queries_total{app=~\"$app\"}[$__range])", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": "Prometheus", "exemplar": false, "expr": "sum by (app) (increase(voedger_qp_errors_total{app=~\"$app\"}[$__range]))", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}], "title": "Overall QP", "transformations": [{"id": "merge", "options": {}}], "type": "table"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 69, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "sum(increase(voedger_cp_exec_seconds{app=~\"$filtered_app\"}[$aggregation_interval]))*1000/voedger_cp_commands_total{app=~\"$filtered_app\"}/sum(increase(voedger_cp_commands_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Exec", "range": true, "refId": "C"}, {"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "sum(increase(voedger_cp_projectors_seconds{app=~\"$filtered_app\"}[$aggregation_interval]))*1000/sum(increase(voedger_cp_commands_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Projectors", "range": true, "refId": "D"}, {"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "((sum(increase(voedger_cp_commands_seconds{app=~\"$filtered_app\"}[$aggregation_interval])) - sum(increase(voedger_cp_projectors_seconds{app=~\"$filtered_app\"}[$aggregation_interval])) - sum(increase(voedger_cp_exec_seconds{app=~\"$filtered_app\"}[$aggregation_interval]))))*1000/sum(increase(voedger_cp_commands_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "hide": false, "interval": "", "legendFormat": "Rest", "range": true, "refId": "A"}], "title": "CP latency stack, avg per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 34}, "id": 90, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Exec", "range": true, "refId": "C"}, {"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "(sum(increase(voedger_qp_exec_fields_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)) + sum(increase(voedger_qp_exec_enrich_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)) + sum(increase(voedger_qp_exec_filter_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)) + sum(increase(voedger_qp_exec_order_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)) + sum(increase(voedger_qp_exec_count_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)) + sum(increase(voedger_qp_exec_send_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0)))/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]) > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Rows processing", "range": true, "refId": "D"}], "title": "QP latency stack, avg per $aggregation_interval", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 41}, "id": 61, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_fields_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Exec: Fields", "range": true, "refId": "C"}, {"datasource": "Prometheus", "editorMode": "code", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_enrich_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "interval": "", "legendFormat": "Exec: <PERSON><PERSON>", "range": true, "refId": "D"}, {"datasource": "Prometheus", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_filter_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "hide": false, "interval": "", "legendFormat": "Exec: <PERSON><PERSON>", "refId": "A"}, {"datasource": "Prometheus", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_order_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "hide": false, "interval": "", "legendFormat": "Exec: Order", "refId": "B"}, {"datasource": "Prometheus", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_count_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "hide": false, "interval": "", "legendFormat": "Exec: Count", "refId": "E"}, {"datasource": "Prometheus", "exemplar": true, "expr": "sum(increase(voedger_qp_exec_send_seconds{app=~\"$filtered_app\"}[$aggregation_interval]) or vector(0))*1000/sum(increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval])  > 0 or on() increase(voedger_qp_queries_total{app=~\"$filtered_app\"}[$aggregation_interval]))", "hide": false, "interval": "", "legendFormat": "Exec: Send", "refId": "F"}], "title": "QP rows processing latency stack, avg per $aggregation_interval", "type": "timeseries"}], "repeat": "filtered_app", "title": "Latency stacks: $filtered_app", "type": "row"}], "refresh": "", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "definition": "label_values(app)", "hide": 0, "includeAll": true, "label": "App", "multi": false, "name": "app", "options": [], "query": {"query": "label_values(app)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "1h", "value": "1h"}, "hide": 0, "includeAll": false, "label": "Aggregation interval", "multi": false, "name": "aggregation_interval", "options": [{"selected": true, "text": "1h", "value": "1h"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "1h,1m,5m,30m,1d", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "definition": "label_values(app)", "hide": 2, "includeAll": true, "label": "filtered_app", "multi": false, "name": "filtered_app", "options": [], "query": {"query": "label_values(app)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^(?!sys\\/blobber$|sys\\/router$).*$/", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "UTC", "title": "App Processors", "uid": "DMxOsLJSk2", "version": 114, "weekStart": ""}