[{"height": 557, "image": 485, "name": "Beach", "num": 3, "preview": 536, "sys.ID": 1, "sys.IsActive": true, "sys.QName": "app1pkg.air_table_plan", "width": 879}, {"height": 558, "image": 473, "name": "First floor", "num": 0, "preview": 541, "sys.ID": 2, "sys.IsActive": true, "sys.QName": "app1pkg.air_table_plan", "width": 882}, {"backup_printer": "", "bottom_lines": 6, "cant_be_redirected_to": 0, "check_status": 0, "codepage": 850, "com_params": "cG9ydD0xDQpiYXVkcmF0ZT05NjAwDQpkZWZhdWx0cz1vbg==", "con": 0, "connection_type": 1, "dont_auto_open_drawer": 1, "driver_id": "", "driver_kind": 0, "driver_params": "", "error_flag": 0, "exclude_message": 0, "fiscal": 0, "guid": "RUVBOUQ1MTYyNkE3OUY0QUFDNzUyNDc0MTg1MUFDREI=", "hht_printer_port": 1, "name": "1.<PERSON>", "null_print": 1, "port": 5, "printer_ip": "***************", "printer_port": 9100, "printer_type": 0, "speed": 9600, "sys.ID": 3, "sys.IsActive": true, "sys.QName": "app1pkg.printers", "top_lines": 0, "width": 42}, {"auto_accept_reservations": 0, "bmanual": 0, "close_manualy": 0, "group_vat_level": 0, "name": "Restaurant", "number": 0, "only_reserved": 0, "sc": 0, "sccovers": 0, "sys.ID": 4, "sys.IsActive": true, "sys.QName": "app1pkg.sales_area"}, {"driver_id": "", "driver_kind": 0, "guid": "7238FE86-172F-4295-8267-4F50A72D1B50", "kind": 1, "name": "Visa", "number": 2, "params": "", "paym_external_id": "", "psp_model": -1, "sys.ID": 5, "sys.IsActive": true, "sys.QName": "app1pkg.payments"}, {"block_time_break": 0, "datebirth": 0, "exclude_message": 0, "firstname": "<PERSON>", "is_custom_remoteterm_poscode": 0, "language": "0000", "language_char": 0, "lastname": "Broom", "lefthand": 1, "mandates": "ezU2REFGRDNBLUIxODgtNDNBNS1CMzM3LUVGRTg0REI4OEMzM30NCnswODNDQzA1RC00NTY5LTQwNTctQUY2Ny0xRkVCN0IzNTJDQjF9DQo=", "name": "<PERSON>", "needcashdeclaration": 0, "not_print_waiter_report": 0, "number": 3, "personal_drawer": 0, "start_week_day": 0, "start_week_time": 28800000, "sys.ID": 6, "sys.IsActive": true, "sys.QName": "app1pkg.untill_users", "user_clock_in": 0, "user_poscode": "6", "user_poscode_remoteterm": "", "user_training": 0, "user_transfer": 1, "user_void": 1, "void_number": 0}, {"sys.QName": "app1pkg.computers", "sys.ID": 7}, {"sys.QName": "app1pkg.restaurant_computers", "sys.ID": 8, "id_computers": 7, "sys.ParentID": 7, "sys.Container": "restaurant_computers"}]