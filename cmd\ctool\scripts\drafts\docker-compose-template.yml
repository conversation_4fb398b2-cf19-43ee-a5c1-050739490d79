# Copyright (c) 2023 Sigma-Soft, Ltd.
# <AUTHOR> Ponomarev

version: '3.5'

services:

  scylla1:
    image: scylladb/scylla:5.1.13
    command: --seeds {{.DBNode1}},{{.DBNode2}},{{.DBNode3}} --listen-address {{.DBNode1}} --developer-mode 0 --io-setup 0
    ports:
      - target: 9042
        published: 9042
        protocol: tcp
        mode: host
      - target: 7000
        published: 7000
        protocol: tcp
        mode: host
      - target: 9180
        published: 9180
        protocol: tcp
        mode: host
    deploy:
      placement:
        constraints:
          - node.labels.DBNode1 == true
      replicas: 1
    healthcheck:
      test: ["CMD-SHELL", "nodetool status | awk '/^UN/ {print $$2}' | grep -w $$(getent hosts {{.DBNode1}} | awk '{print $$1}')"]
      interval: 15s
      timeout: 5s
      retries: 30
    networks:
      - outside
    volumes:
      - scylla1-data:/var/lib/scylla
      - ~/scylla/scylla.yaml:/etc/scylla/scylla.yaml
      - ~/scylla.d:/etc/scylla.d
      - /etc/hosts:/etc/hosts
      - ~/scylla-node-exporter/scylla-node-exporter:/etc/default/scylla-node-exporter
      - /etc/node-exporter:/etc/node-exporter

  scylla2:
    image: scylladb/scylla:5.1.13
    command: --seeds {{.DBNode1}},{{.DBNode2}},{{.DBNode3}} --listen-address {{.DBNode2}} --developer-mode 0 --io-setup 0
    ports:
      - target: 9042
        published: 9042
        protocol: tcp
        mode: host
      - target: 7000
        published: 7000
        protocol: tcp
        mode: host
      - target: 9180
        published: 9180
        protocol: tcp
        mode: host
    deploy:
      placement:
        constraints:
          - node.labels.DBNode2 == true
      replicas: 1
    healthcheck:
      test: ["CMD-SHELL", "nodetool status | awk '/^UN/ {print $$2}' | grep -w $$(getent hosts {{.DBNode2}} | awk '{print $$1}')"]
      interval: 15s
      timeout: 5s
      retries: 30
    networks:
      - outside
    volumes:
      - scylla2-data:/var/lib/scylla
      - ~/scylla/scylla.yaml:/etc/scylla/scylla.yaml
      - ~/scylla.d:/etc/scylla.d
      - /etc/hosts:/etc/hosts
      - ~/scylla-node-exporter/scylla-node-exporter:/etc/default/scylla-node-exporter
      - /etc/node-exporter:/etc/node-exporter

  scylla3:
    image: scylladb/scylla:5.1.13
    command: --seeds {{.DBNode1}},{{.DBNode2}},{{.DBNode3}} --listen-address {{.DBNode3}} --developer-mode 0 --io-setup 0
    ports:
      - target: 9042
        published: 9042
        protocol: tcp
        mode: host
      - target: 7000
        published: 7000
        protocol: tcp
        mode: host
      - target: 9180
        published: 9180
        protocol: tcp
        mode: host
    deploy:
      placement:
        constraints:
          - node.labels.DBNode3 == true
      replicas: 1
    healthcheck:
      test: ["CMD-SHELL", "nodetool status | awk '/^UN/ {print $$2}' | grep -w $$(getent hosts {{.DBNode3}} | awk '{print $$1}')"]
      interval: 15s
      timeout: 5s
      retries: 30
    networks:
      - outside
    volumes:
      - scylla3-data:/var/lib/scylla
      - ~/scylla/scylla.yaml:/etc/scylla/scylla.yaml
      - ~/scylla.d:/etc/scylla.d
      - /etc/hosts:/etc/hosts
      - ~/scylla-node-exporter/scylla-node-exporter:/etc/default/scylla-node-exporter
      - /etc/node-exporter:/etc/node-exporter

volumes:
  scylla1-data:
    driver: local
    driver_opts:
      type: node
      o: bind
      device: /var/lib/scylla
  scylla2-data:
    driver: local
    driver_opts:
      type: node
      o: bind
      device: /var/lib/scylla
  scylla3-data:
    driver: local
    driver_opts:
      type: node
      o: bind
      device: /var/lib/scylla
networks:
  outside:
    name: host
    external: true
