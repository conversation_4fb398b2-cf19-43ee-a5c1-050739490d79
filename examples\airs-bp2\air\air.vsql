-- Copyright (c) 2024-present unTill Software Development Group B. V.
-- <AUTHOR>

IMPORT SCHEMA 'untill';

APPLICATION AIR(
    USE untill;
);

WORKSPACE RestaurantWS INHERITS untill.SomeWS (
    DESCRIPTOR RestaurantDescriptor();

	TABLE ProformaPrinted INHERITS sys.ODoc (
		Number int32 NOT NULL,
		UserID ref(untill.untill_users) NOT NULL,
		Timestamp int64 NOT NULL,
		BillID ref(untill.bill) NOT NULL
	);

	TYPE CmdPBillResult (
		Number int32 NOT NULL
	);

	VIEW PbillDates (
		Year int32 NOT NULL,
		DayOfYear int32 NOT NULL,
		FirstOffset int64 NOT NULL,
		LastOffset int64 NOT NULL,
		PRIMARY KEY ((Year), DayOfYear)
	) AS RESULT OF FillPbillDates;

	TABLE NextNumbers INHERITS sys.WSingleton (
		NextPBillNumber int32
	);

	EXTENSION ENGINE BUILTIN (
        COMMAND Orders(untill.orders);
        COMMAND Pbill(untill.pbill) RETURNS CmdPBillResult;
	    PROJECTOR FillPbillDates AFTER EXECUTE ON Pbill INTENTS(sys.View(PbillDates));
	);
)