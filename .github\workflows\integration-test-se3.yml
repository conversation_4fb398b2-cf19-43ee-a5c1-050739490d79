# Copyright (c) 2024 Sigma-Soft, Ltd.
# <AUTHOR>
#
# To set up a secret in your GitHub repository, follow these steps:
#    - Go to your repository in GitHub and click on the "Settings" tab.
#    - In the left sidebar, click on "Secrets".
#    - Click on the "New secret" button.
#    - Enter a name for your secret (e.g., "AWS_ACCESS_KEY_ID"), and paste in the value of your AWS access key ID.
#    - Click on the "Add secret" button to save the secret.
# You can repeat these steps for each secret you need to store:
#    -  such as your AWS secret access key
#    -  SSH private key
#    -  and any other sensitive information.
# To use the secrets in your GitHub Actions workflow, you can reference them using the syntax ${{ secrets.SECRET_NAME }}.

name: ctool se3 integration test

on:
  issues:
    types: [opened]

jobs:


  deploy:
    if: ${{ github.event.issue.title == 'ctoolintegrationtest se3' }}
    runs-on: ubuntu-22.04
    env:
      SSH_PORT: 2214
      SSH_OPTIONS: "-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -o LogLevel=ERROR"
      TF_VAR_ssh_private_key: ${{ secrets.AWS_SSH_KEY }}
      TF_VAR_gh_token: ${{ secrets.REPOREADING_TOKEN }}
      TF_VAR_git_commit_id: ""
      TF_VAR_git_repo_url: "https://github.com/voedger/voedger"
      TF_VAR_ssh_port: 2214
      TF_VAR_issue_number: ${{ github.event.issue.number }}
      TF_VAR_included_nodes: '["node_00", "node_01", "node_02", "node_instead_00", "node_instead_01"]'

    steps:
      - name: Check Issue
        run: |
      
          ORG_NAME="voedger"
          TEAM_NAME="DevOps_ctool"
          USER_NAME="${{ github.event.issue.user.login }}"
      
          # Check organization membership
          ORG_MEMBERSHIP=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer ${{ secrets.REPOREADING_TOKEN }}" "https://api.github.com/orgs/$ORG_NAME/members/$USER_NAME")
      
          if [[ $ORG_MEMBERSHIP -eq 204 ]]; then
            echo "The user $USER_NAME is a member of the organization $ORG_NAME."
          else
           echo "The user $USER_NAME is not a member of the organization $ORG_NAME."
            exit 1
          fi
      
          # Check team membership
          TEAM_MEMBERSHIP=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer ${{ secrets.REPOREADING_TOKEN }}" "https://api.github.com/orgs/$ORG_NAME/teams/$TEAM_NAME/memberships/$USER_NAME")
      
          if [[ $TEAM_MEMBERSHIP -eq 200 ]]; then
            echo "The user $USER_NAME is a member of the team $TEAM_NAME within the organization $ORG_NAME."
          else
            echo "The user $USER_NAME is not a member of the team $TEAM_NAME within the organization $ORG_NAME."
            exit 1
          fi

      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Create Infrastructure
        uses: ./.github/actions/infrastructure-create-action
        with:
          terraform_config_path: 'cmd/ctool/scripts/terraform/'
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.AWS_SSH_KEY }}

      - name: Load environment file
        run: |
          echo "PUBLIC_IP=$(terraform -chdir=cmd/ctool/scripts/terraform/ output -raw public_ip_node_00)" >> $GITHUB_ENV
          echo "CTOOL_IP=$(terraform -chdir=cmd/ctool/scripts/terraform/ output -raw public_ip_node_00)" >> $GITHUB_ENV
          echo "MON_PASSWORD=test_voedger_pass" >> $GITHUB_ENV
          echo "MON_USER=voedger" >> $GITHUB_ENV
          echo "SSH_PORT=2214" >> $GITHUB_ENV
          if [ -n "${SSH_PORT:-}" ]; then
              SSH_OPTIONS+=" -p $SSH_PORT"
          fi
          echo "SSH_OPTIONS=$SSH_OPTIONS" >> $GITHUB_ENV

      - name: Init Cluster
        uses: ./.github/actions/cluster-init-action
        with:
          command: "./ctool init SE --acme-domain ${{ github.event.issue.number }}-01.cdci.voedger.io ********* ********* ********* -p ${{ env.SSH_PORT }} -v --ssh-key /tmp/amazonKey.pem"

      - name: Run Voedger Cluster Tests
        uses: ./.github/actions/cluster-test-action
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}


      - name: Terraform destroy
        if: always()
        run: terraform -chdir=cmd/ctool/scripts/terraform/ destroy -auto-approve
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Add comment to issue
        if: ${{ always() }}
        run: |
          curl --request POST \
            --url https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments \
            --header 'Authorization: Bearer ${{ secrets.REPOREADING_TOKEN }}' \
            --header 'Content-Type: application/json' \
            --data '{
              "body": "This is a comment that will be automatic added to issue #${{ github.event.issue.number }} by the GitHub Action.\nThe result of the GitHub Action is ${{ job.status }}."
            }'

  upgrade:
    needs: deploy
    if: ${{ github.event.issue.title == 'ctoolintegrationtest se3' }}
    runs-on: ubuntu-22.04
    env:
      SSH_PORT: 2214
      SSH_OPTIONS: "-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -o LogLevel=ERROR"
      TF_VAR_ssh_private_key: ${{ secrets.AWS_SSH_KEY }}
      TF_VAR_gh_token: ${{ secrets.REPOREADING_TOKEN }}
      TF_VAR_git_commit_id: "7f00f4c759b23c46754a201266cbe94fa8c3777c"
      TF_VAR_git_repo_url: "https://github.com/voedger/voedger"
      TF_VAR_ssh_port: 2214
      TF_VAR_issue_number: ${{ github.event.issue.number }}
      TF_VAR_included_nodes: '["node_00", "node_01", "node_02", "node_instead_00", "node_instead_01"]'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Create Infrastructure
        uses: ./.github/actions/infrastructure-create-action
        with:
          terraform_config_path: 'cmd/ctool/scripts/terraform/'
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.AWS_SSH_KEY }}

      - name: Load environment file
        run: |
          echo "PUBLIC_IP=$(terraform -chdir=cmd/ctool/scripts/terraform/ output -raw public_ip_node_00)" >> $GITHUB_ENV
          echo "CTOOL_IP=$(terraform -chdir=cmd/ctool/scripts/terraform/ output -raw public_ip_node_00)" >> $GITHUB_ENV
          echo "MON_PASSWORD=test_voedger_pass" >> $GITHUB_ENV
          echo "MON_USER=voedger" >> $GITHUB_ENV
          echo "SSH_PORT=2214" >> $GITHUB_ENV
          if [ -n "${SSH_PORT:-}" ]; then
              SSH_OPTIONS+=" -p $SSH_PORT"
          fi
          echo "SSH_OPTIONS=$SSH_OPTIONS" >> $GITHUB_ENV

      - name: Init Cluster
        uses: ./.github/actions/cluster-init-action
        with:
          command: "./ctool init SE ********* ********* ********* -p ${{ env.SSH_PORT }} -v --ssh-key /tmp/amazonKey.pem"

      - name: Wait for db cluster building
        run: |
          echo "Work with ${{ env.PUBLIC_IP }}"
          count=0
          while [ $count -lt 60 ]; do
             if [ $(ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} docker exec '$(docker ps -qf name=scylla)' nodetool status | grep -c "^UN\s") -eq 3 ]; then
             echo "Scylla initialization success"
               break
             fi
             echo "Still wait for scylla initialization.."
             sleep 5
             count=$((count+1))
          done
          if [ $count -eq 60 ]; then
             echo "Scylla initialization timed out."
             exit 1
          fi

      - name: Upgrade Voedger Cluster
        run: |
          ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} <<EOF 
             cd /home/<USER>/voedger/cmd/ctool
             ./ctool version
             git checkout -b upgrade-test
             git checkout main
             git pull origin main
             git log -n 1
             go build -o ctool
             ./ctool version
             ./ctool upgrade -v --ssh-key /tmp/amazonKey.pem
          EOF

      - name: Add ACME domain
        run: |
          ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} <<EOF 
             cd /home/<USER>/voedger/cmd/ctool
             ./ctool acme add ${{ github.event.issue.number }}-01.cdci.voedger.io -v --ssh-key /tmp/amazonKey.pem
          EOF

      - name: Run Voedger Cluster Tests
        uses: ./.github/actions/cluster-test-action
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Terraform destroy
        if: always()
        run: terraform -chdir=cmd/ctool/scripts/terraform/ destroy -auto-approve  -var="git_commit_id=7f00f4c759b23c46754a201266cbe94fa8c3777c"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Add comment to issue
        if: ${{ always() }}
        run: |
          curl --request POST \
            --url https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments \
            --header 'Authorization: Bearer ${{ secrets.REPOREADING_TOKEN }}' \
            --header 'Content-Type: application/json' \
            --data '{
              "body": "This is a comment that will be automatic added to issue #${{ github.event.issue.number }} by the GitHub Action.\nThe result of the GitHub Action is ${{ job.status }}."
            }'
  

