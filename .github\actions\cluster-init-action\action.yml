# Copyright (c) 2023 Sigma-Soft, Ltd.
# <AUTHOR>
# @date 2023-12-25

name: 'Voedger Cluster Init Action'
description: 'Voedger Cluster Init Action'

inputs:
  command:
    description: 'ctool command to run'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Test deploy cluster. Simulate error when init. Continue with repeat after problem elimination.
      run: |
        if ! ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} "cd /home/<USER>/voedger/cmd/ctool && ${{ inputs.command }}; exit \$?"; then
          echo "Error: SSH key permission too open."
            if ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} "chmod 400 /tmp/amazonKey.pem; exit \$?"; then
              echo "Changing ssh key permissions to more restrective."
                if ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} "cd /home/<USER>/voedger/cmd/ctool && ./ctool repeat -v --ssh-key /tmp/amazonKey.pem; exit \$?"; then
                  echo "Cluster init reepeat succesfull." 
                else 
                  echo "Error: cluster init repeat. Exit."
                  exit 1
                fi 
            else 
              echo "Error: changing ssh key permissions to more restrective. Exit."
              exit 1
            fi 
        else 
          echo "Error: connect to resources with too open ssh key not possible. Exit."
          exit 1
        fi
      shell: bash

