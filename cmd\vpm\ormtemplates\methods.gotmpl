{{define "methods"}}

func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) PkgPath() string {
	return Package_{{.Package.Name}}.Path
}

func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) Entity() string {
	return "{{.Name}}"
}

{{if (eq .Type "WS")}}
func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) Descriptor() string {
	return "{{.WsDescriptor}}"
}
{{end}}

{{if (eq .Type "WDoc")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) IAmWDoc() {
	return
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) QName() exttinygo.QName {
	return exttinygo.QName{FullPkgName: v.PkgPath(), Entity: v.Entity()}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Insert(id ID) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	newVal := exttinygo.NewValue(kb)
	newVal.PutInt64(FieldNameSysID, int64(id))
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: newVal}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Update(id ID) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	existingValue := v.MustGet(id)
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(kb, existingValue.tv)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Get(id ID) (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	tv, exists := exttinygo.QueryValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}, exists
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) MustGet(id ID) Value_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	tv := exttinygo.MustGetValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Insert() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(v.kb)}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Update() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(v.kb, v.tv)}
}
{{end}}

{{if (eq .Type "WSingleton")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Insert() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutBool(sys.Storage_Record_Field_IsSingleton, true)
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(kb)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Update() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	existingValue := v.MustGet()
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutBool(sys.Storage_Record_Field_IsSingleton, true)
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(kb, existingValue.tv)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Get() (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutBool(sys.Storage_Record_Field_IsSingleton, true)
	tv, exists := exttinygo.QueryValue(kb)
	if !exists {
		return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{kb: kb}, false
	}
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}, true
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) MustGet() Value_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutBool(sys.Storage_Record_Field_IsSingleton, true)
	tv := exttinygo.MustGetValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Insert() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(v.kb)}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Update() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(v.kb, v.tv)}
}
{{end}}

{{if (eq .Type "CDoc")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) IAmCDoc() {
	return
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) QName() exttinygo.QName {
	return exttinygo.QName{FullPkgName: v.PkgPath(), Entity: v.Entity()}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) MustGet(id ID) Value_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: exttinygo.MustGetValue(kb)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Get(id ID) (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	tv, exists := exttinygo.QueryValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv}, exists
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Insert(id ID) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	newVal := exttinygo.NewValue(kb)
	newVal.PutInt64(FieldNameSysID, int64(id))
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: newVal}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Update(id ID) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	existingValue := v.MustGet(id)
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(kb, existingValue.tv)}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Insert() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(v.kb)}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Update() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(v.kb, v.tv)}
}
{{end}}

{{if (eq .Type "ODoc")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) IAmODoc() {
	return
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) QName() exttinygo.QName {
	return exttinygo.QName{FullPkgName: v.PkgPath(), Entity: v.Entity()}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Insert(id ID) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	newVal := exttinygo.NewValue(kb)
	newVal.PutInt64(FieldNameSysID, int64(id))
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: newVal}
}
{{end}}

{{if (eq .Type "ORecord")}}
func (v *Container_ORecord_{{.Package.Name}}_{{.Name}}) Len() int {
	if v.len == 0 {
		v.len = v.tv.Len() + 1
	}

	return v.len - 1
}

func (v *Container_ORecord_{{.Package.Name}}_{{.Name}}) Get(i int) Value_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: v.tv.GetAsValue(i)}
}
{{end}}

{{if (eq .Type "View")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Insert({{range .Keys}}{{lower .Name}} {{.Type}}, {{end}}) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageView, v.fQName){{range .Keys}}
	{{if eq .Type "ID"}}kb.PutInt64("{{.Name}}", int64({{lower .Name}})){{else}}kb.Put{{capitalize .Type}}("{{.Name}}", {{lower .Name}}){{end}}{{end}}
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(kb)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Keys() []string {
	return []string{ {{range .Keys}}"{{.Name}}", {{end}}
	}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Update({{range .Keys}}{{lower .Name}} {{.Type}}, {{end}}) Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	existingValue := v.MustGet({{range .Keys}}{{lower .Name}}, {{end}})
	kb := exttinygo.KeyBuilder(exttinygo.StorageView, v.fQName){{range .Keys}}
	{{if eq .Type "ID"}}kb.PutInt64("{{.Name}}", int64({{lower .Name}})){{else}}kb.Put{{capitalize .Type}}("{{.Name}}", {{lower .Name}}){{end}}{{end}}
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(kb, existingValue.tv)}
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Get({{range .Keys}}{{lower .Name}} {{.Type}}, {{end}}) (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	kb := exttinygo.KeyBuilder(exttinygo.StorageView, v.fQName){{range .Keys}}
	{{if eq .Type "ID"}}kb.PutInt64("{{.Name}}", int64({{lower .Name}})){{else}}kb.Put{{capitalize .Type}}("{{.Name}}", {{lower .Name}}){{end}}{{end}}
	tv, exists := exttinygo.QueryValue(kb)
	if !exists {
		return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{kb: kb}, false
	}
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}, true
}

func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) MustGet({{range .Keys}}{{lower .Name}} {{.Type}}, {{end}}) Value_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageView, v.fQName){{range .Keys}}
	{{if eq .Type "ID"}}kb.PutInt64("{{.Name}}", int64({{lower .Name}})){{else}}kb.Put{{capitalize .Type}}("{{.Name}}", {{lower .Name}}){{end}}{{end}}
	tv := exttinygo.MustGetValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv, kb: kb}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Insert() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.NewValue(v.kb)}
}

func (v Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) Update() Intent_{{.Type}}_{{.Package.Name}}_{{.Name}} {
	return Intent_{{.Type}}_{{.Package.Name}}_{{.Name}}{intent: exttinygo.UpdateValue(v.kb, v.tv)}
}
{{end}}

{{if or (eq .Type "Container")}}
func (v {{.Type}}_{{.Package.Name}}_{{.Name}}) Get(id ID) (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	kb := exttinygo.KeyBuilder(exttinygo.StorageRecord, v.fQName)
	kb.PutRecordID(sys.Storage_Record_Field_ID, int64(id))
	tv, exists := exttinygo.QueryValue(kb)
	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: tv}, exists
}
{{end}}

{{if or (eq .Type "Command") (eq .Type "Query")}}
{{if .ArgumentObject}}
func (c {{.Type}}_{{.Package.Name}}_{{.Name}}) ArgumentObject() Value_{{.ArgumentObject.Type}}_{{.ArgumentObject.Package.Name}}_{{.ArgumentObject.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageCommandContext, exttinygo.NullEntity)
	return Value_{{.ArgumentObject.Type}}_{{.ArgumentObject.Package.Name}}_{{.ArgumentObject.Name}}{tv: exttinygo.MustGetValue(kb).AsValue(sys.Storage_Event_Field_ArgumentObject)}
}
{{end}}

func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) ArgumentPkgPath() string {
	{{if .ArgumentObject}}return Package_{{.ArgumentObject.Package.Name}}.{{.ArgumentObject.Type}}_{{.ArgumentObject.Name}}.PkgPath()
	{{else}}return ""{{end}}
}

func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) ArgumentEntity() string {
	{{if .ArgumentObject}}return Package_{{.ArgumentObject.Package.Name}}.{{.ArgumentObject.Type}}_{{.ArgumentObject.Name}}.Entity()
	{{else}}return ""{{end}}
}

{{if (eq .Type "Command")}}
func (r {{.Type}}_{{.Package.Name}}_{{.Name}}) WorkspaceDescriptor() string {
	{{if (eq .WsDescriptor "")}}return ""{{else}}return Package_{{.Package.Name}}.WS_{{.WsName}}.Descriptor(){{end}}
}
{{end}}

{{if .UnloggedArgumentObject}}
func (c {{.Type}}_{{.Package.Name}}_{{.Name}}) UnloggedArgumentObject() Value_{{.UnloggedArgumentObject.Type}}_{{.UnloggedArgumentObject.Package.Name}}_{{.UnloggedArgumentObject.Name}} {
	kb := exttinygo.KeyBuilder(exttinygo.StorageCommandContext, exttinygo.NullEntity)
	return Value_{{.UnloggedArgumentObject.Type}}_{{.UnloggedArgumentObject.Package.Name}}_{{.UnloggedArgumentObject.Name}}{tv: exttinygo.MustGetValue(kb).AsValue(FieldNameEventUnloggedArgumentObject)}
}
{{end}}

{{if .ResultObjectFields}}
func (c {{.Type}}_{{.Package.Name}}_{{.Name}}) Result({{range .ResultObjectFields}}{{lower .Name}} {{.Type}}, {{end}}) {
	__result := exttinygo.NewValue(exttinygo.KeyBuilder(exttinygo.StorageResult, exttinygo.NullEntity)){{range .ResultObjectFields}}
	{{if eq .Type "ID"}}__result.PutInt64("{{.Name}}", int64({{lower .Name}})){{else}}__result.Put{{capitalize .Type}}("{{.Name}}", {{lower .Name}}){{end}}{{end}}
}
{{end}}
{{end}}

{{if (eq .Type "Projector")}}

{{range .On}}

{{if (doesExecuteOn .)}}

{{if not .SkipGeneration}}
type Cmd_{{.Projector.Package.Name}}_{{.Name}} struct {
	qname string
	event exttinygo.TValue
}

{{if .EventItem.ArgumentObject}}
func (c Cmd_{{.Projector.Package.Name}}_{{.Name}}) Arg() (Value_{{.EventItem.ArgumentObject.Type}}_{{.EventItem.ArgumentObject.Package.Name}}_{{.EventItem.ArgumentObject.Name}}, bool) {
	qname := c.event.AsQName(sys.Storage_Event_Field_QName)
	if qname.FullPkgName != Package_{{.EventItem.Package.Name}}.{{.EventItem.Type}}_{{.EventItem.Name}}.PkgPath() || qname.Entity != Package_{{.EventItem.Package.Name}}.{{.EventItem.Type}}_{{.EventItem.Name}}.Entity() {
		return Value_{{.EventItem.ArgumentObject.Type}}_{{.EventItem.ArgumentObject.Package.Name}}_{{.EventItem.ArgumentObject.Name}}{}, false
	}

	return Value_{{.EventItem.ArgumentObject.Type}}_{{.EventItem.ArgumentObject.Package.Name}}_{{.EventItem.ArgumentObject.Name}}{tv: c.event.AsValue(sys.Storage_Event_Field_ArgumentObject)}, true
}

func (c Cmd_{{.Projector.Package.Name}}_{{.Name}}) Event() Event {
	return Event{
		WLogOffset: c.event.AsInt64(sys.Storage_Event_Field_WLogOffset),
	}
}
{{end}}
{{end}}

func (p *Projector_{{.Projector.Package.Name}}_{{.Projector.Name}}) Cmd_{{.Name}}() Cmd_{{.Projector.Package.Name}}_{{.Name}} {
	return Cmd_{{.Projector.Package.Name}}_{{.Name}}{
		qname: p.PkgPath() + "." + "{{.Name}}",
		event: p.event(),
	}
}

{{end}}

{{if (doesTriggerOnCUD .)}}

func (p *Projector_{{.Projector.Package.Name}}_{{.Projector.Name}}) CUDs_{{.Package.Name}}_{{.Name}}() iter.Seq[Value_{{.Type}}_{{.Package.Name}}_{{.Name}}] {
	return func(yield func(Value_{{.Type}}_{{.Package.Name}}_{{.Name}}) bool) {
		cudsValue := p.event().AsValue(sys.Storage_WLog_Field_CUDs)
		for i := 0; i < cudsValue.Len(); i++ {
			cudValue := cudsValue.GetAsValue(i)
			cudQName := cudValue.AsQName(FieldNameSysQName)
			if cudQName.FullPkgName == Package_{{.Package.Name}}.{{.Type}}_{{.Name}}.PkgPath() && cudQName.Entity == Package_{{.Package.Name}}.{{.Type}}_{{.Name}}.Entity() {
				if !yield(Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: cudValue}) {
					return
				}
			}
		}
	}
}

{{end}}

{{if (doesExecuteWithParam .)}}

func (p *Projector_{{.Projector.Package.Name}}_{{.Projector.Name}}) Arg_{{.Package.Name}}_{{.Name}}() (Value_{{.Type}}_{{.Package.Name}}_{{.Name}}, bool) {
	arg := p.event().AsValue(sys.Storage_Event_Field_ArgumentObject)
	argQName := arg.AsQName(FieldNameSysQName)
	if argQName.FullPkgName != Package_{{.Package.Name}}.{{.Type}}_{{.Name}}.PkgPath() || argQName.Entity != Package_{{.Package.Name}}.{{.Type}}_{{.Name}}.Entity() {
		return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{}, false
	}

	return Value_{{.Type}}_{{.Package.Name}}_{{.Name}}{tv: arg}, true
}
{{end}}

{{end}}

{{end}}



{{end}}

