
version: '3.7'

services:
  
  voedger:
    image: voedger/voedger:0.0.1-alpha
    networks:
      - outside
    volumes:
      - /etc/hosts:/etc/hosts
    environment:
      - VOEDGER_HTTP_PORT=443
      - VOEDGER_ACME_DOMAINS=${VOEDGER_ACME_DOMAINS}
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.AppNode == true

    restart: unless-stopped

networks:
  outside:
    name: host
    external: true
