// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/voedger/voedger/cmd/voedger/voedgerimpl"
	"github.com/voedger/voedger/pkg/ihttp"
	"github.com/voedger/voedger/pkg/ihttpctl"
	"github.com/voedger/voedger/pkg/ihttpimpl"
	"github.com/voedger/voedger/pkg/istorage"
	"github.com/voedger/voedger/pkg/istorage/provider"
)

import (
	_ "embed"
)

// Injectors from wire.go:

func wireServer(httpCliParams ihttp.CLIParams, appsCliParams voedger.CLIParams) (WiredServer, func(), error) {
	iAppStorageFactory, err := voedger.NewAppStorageFactory(appsCliParams)
	if err != nil {
		return WiredServer{}, nil, err
	}
	iAppStorageProvider := provideAppStorageProvider(iAppStorageFactory)
	iRouterStorage, err := ihttp.NewIRouterStorage(iAppStorageProvider)
	if err != nil {
		return WiredServer{}, nil, err
	}
	ihttpProcessor, cleanup := ihttpimpl.NewProcessor(httpCliParams, iRouterStorage)
	v := voedger.NewStaticEmbeddedResources()
	redirectRoutes := voedger.NewRedirectionRoutes()
	defaultRedirectRoute := voedger.NewDefaultRedirectionRoute()
	acmeDomains := httpCliParams.AcmeDomains
	appRequestHandlers := voedger.NewAppRequestHandlers()
	ihttpProcessorController := ihttpctl.NewHTTPProcessorController(ihttpProcessor, v, redirectRoutes, defaultRedirectRoute, acmeDomains, appRequestHandlers)
	wiredServer := WiredServer{
		IHTTPProcessor:           ihttpProcessor,
		IHTTPProcessorController: ihttpProcessorController,
	}
	return wiredServer, func() {
		cleanup()
	}, nil
}

// wire.go:

// provideAppStorageProvider is intended to be used by wire instead of istorage/provider.Provide, because wire can not handle variadic arguments
func provideAppStorageProvider(appStorageFactory istorage.IAppStorageFactory) istorage.IAppStorageProvider {
	return provider.Provide(appStorageFactory)
}
