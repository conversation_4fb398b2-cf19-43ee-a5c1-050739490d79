# Copyright (c) 2023 Sigma-Soft, Ltd.
# <AUTHOR> Ponomarev
# @date 2024-02-15

name: 'Voedger Scylla DB cluster backup action'
description: 'Voedger Scylla DB cluster backup action'

runs:
  using: 'composite'

  steps:
    - name: Smoke test - create keyspace, table and fill with test data
      run: |
        set -x
        ISSUE_TITLE="${{ github.event.issue.title }}"
        
        if [ "$ISSUE_TITLE" == "ctoolintegrationtest ce" ]; then
            REPLICATION_STRATEGY="'class': 'SimpleStrategy', 'replication_factor': 1"
        elif [ "$ISSUE_TITLE" == "ctoolintegrationtest se" -o "$ISSUE_TITLE" == "ctoolintegrationtest se3" ]; then
            REPLICATION_STRATEGY="'class': 'NetworkTopologyStrategy', 'dc1': 2, 'dc2': 1"
        else
            echo "Unknown issue title: $ISSUE_TITLE"
            exit 1
        fi
        
        ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} bash -s <<EOF
        docker exec \$(docker ps -qf name=scylla) bash -c "
        function create_keyspace() {
            cqlsh db-node-1 9042 -e \"CREATE KEYSPACE IF NOT EXISTS v_keyspace WITH replication = {${REPLICATION_STRATEGY}} AND durable_writes = true;\"
        }

        function create_table() {
            cqlsh db-node-1 9042 -k v_keyspace -e \"CREATE TABLE IF NOT EXISTS values (id INT PRIMARY KEY, value TEXT);\"
        }

        function add_records() {
            for ((i=1; i<=10; i++)); do
                cqlsh db-node-1 9042 -k v_keyspace -e \"INSERT INTO values (id, value) VALUES (\\\$i, 'Inserted_value');\"
            done
        }

        function flush_records() {
            nodetool flush v_keyspace values
        }

        create_keyspace
        create_table
        add_records
        flush_records
        "
        EOF
        
      shell: bash

    - name: Check inserted data
      run: |
        set -x
        if [ $(ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} 'docker exec $(docker ps -qf name=scylla) cqlsh db-node-1 9042 -k v_keyspace -e "select count(*) from values" | grep -v rows | grep -Eo "[0-9]+"') -eq 10 ]; then
          echo "Data inserted successfully."
        else
          echo "Failed to insert data."
          exit 1
        fi
      shell: bash

    - name: Backup scylla node
      run: |
        set -x
        ISSUE_TITLE="${{ github.event.issue.title }}"
        
        function get_node_arg() {
            local version_type=$1
            local node_num=$2
            if [ "$version_type" -eq 1 ]; then
                echo ""
            elif [ "$version_type" -eq 3 ]; then
                echo "db-node-$node_num"
            else
                echo "Unsupported value: $version_type"
                return 1
            fi
        }
        
        if [ "$ISSUE_TITLE" == "ctoolintegrationtest ce" ]; then
            CYCLE_END=1
            SSH_KEY_OPTION=""
            SSH_PORT_OPTION="" 
        elif [ "$ISSUE_TITLE" == "ctoolintegrationtest se" -o "$ISSUE_TITLE" == "ctoolintegrationtest se3" ]; then
            CYCLE_END=3
            SSH_KEY_OPTION="--ssh-key /tmp/amazonKey.pem"
            SSH_PORT_OPTION="-p ${{ env.SSH_PORT }}"
        else
          echo "Unknown issue title: $ISSUE_TITLE"
          exit 1
        fi
        
        for ((i=1; i<=CYCLE_END; i++)); do
            NODE_OPTION=$(get_node_arg $CYCLE_END $i)
            if ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} "cd /home/<USER>/voedger/cmd/ctool && ./ctool backup node $NODE_OPTION /home/<USER>/backup $SSH_KEY_OPTION -v $SSH_PORT_OPTION"; then
                echo "Backup success"
            else 
                echo "Failed to backup scylla node"
                exit 1
            fi
        done
      shell: bash

    - name: Drop keyspace
      run: |
        set -euo pipefail
        set -x
        ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} 'docker exec $(docker ps -qf name=scylla) cqlsh db-node-1 9042 -e "DROP KEYSPACE v_keyspace;"'
        ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} 'docker exec $(docker ps -qf name=scylla) cqlsh db-node-1 9042 -e "DESC KEYSPACES;"'
        ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} "sudo rm -rf /var/lib/scylla/data/v_keyspace"
      shell: bash

    - name: Restore keyspace
      run: |
        set -euo pipefail
        set -x
        ISSUE_TITLE="${{ github.event.issue.title }}"
        
        if [ "$ISSUE_TITLE" == "ctoolintegrationtest ce" ]; then
            SSH_KEY_OPTION=""
        elif [ "$ISSUE_TITLE" == "ctoolintegrationtest se" -o "$ISSUE_TITLE" == "ctoolintegrationtest se3" ]; then
            SSH_KEY_OPTION="--ssh-key /tmp/amazonKey.pem"
        else
          echo "Unknown issue title: $ISSUE_TITLE"
          exit 1
        fi
        
        ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.CTOOL_IP }} "cd /home/<USER>/voedger/cmd/ctool && ./ctool restore /home/<USER>/backup $SSH_KEY_OPTION -v"
      shell: bash

    - name: Check inserted data after restore
      run: |
        set -x
        if [ $(ssh ${{ env.SSH_OPTIONS }} ubuntu@${{ env.PUBLIC_IP }} 'docker exec $(docker ps -qf name=scylla) cqlsh db-node-1 9042 -k v_keyspace -e "select count(*) from values" | grep -v rows | grep -Eo "[0-9]+"') -eq 10 ]; then
          echo "Data inserted successfully."
        else
          echo "Failed to insert data."
          exit 1
        fi
      shell: bash
