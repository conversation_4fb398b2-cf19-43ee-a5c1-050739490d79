{"name": "monitor", "version": "0.1.0", "private": true, "homepage": "/static/sys/monitor/site/main/", "dependencies": {"@mui/x-data-grid": "^5.17.8", "@reduxjs/toolkit": "^1.8.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "durations": "^3.4.2", "filesize": "^10.0.5", "human-readable-numbers": "^0.9.5", "moment": "^2.29.4", "prop-types": "^15.8.1", "ra-data-json-server": "^4.4.4", "react": "^18.2.0", "react-admin": "^4.4.4", "react-dom": "^18.2.0", "react-redux": "^8.0.4", "react-scripts": "5.0.1", "recharts": "^2.1.15", "svg-loaders-react": "^2.2.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "BUILD_PATH='../site.main' react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}