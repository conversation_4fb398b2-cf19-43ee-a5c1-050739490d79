# Copyright (c) 2023 Sigma-Soft, Ltd.
# <AUTHOR>
# @date 2023-12-25

name: 'Voedger Cluster Create Infrastructure Action'
description: 'Voedger Cluster Create Infrastructure Action'

inputs:
  terraform_config_path:
    description: 'path to terraform config'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Set up Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_wrapper: false

    - name: Terraform Init
      run: terraform -chdir=${{ inputs.terraform_config_path }} init
      shell: bash

    - name: Terraform plan
      run: terraform -chdir=${{ inputs.terraform_config_path }} plan -out=terraform.tfplan
      shell: bash

    - name: Terraform apply
      run: terraform -chdir=${{ inputs.terraform_config_path }} apply -auto-approve
      shell: bash

