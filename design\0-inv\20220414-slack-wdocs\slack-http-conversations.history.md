https://app.slack.com/client/T02EL4HG54N/C02F5FBE397/thread/C02U7BC6CD6-1641907218.002400

### Query

curl 'https://untillairheeus.slack.com/api/conversations.history?_x_id=8f456579-1651139568.653&_x_csid=AGe2vfNZYyM&slack_route=T02EL4HG54N&_x_version_ts=1651114034&_x_gantry=true&fp=d7' \
  -H 'authority: untillairheeus.slack.com' \
  -H 'accept: */*' \
  -H 'accept-language: en,ru;q=0.9' \
  -H 'cache-control: no-cache' \
  -H 'content-type: multipart/form-data; boundary=----WebKitFormBoundaryhuBPOcj6xHyC6o4c' \
  -H 'cookie: _cs_c=1; _li_dcdm_c=.slack.com; _lc2_fpi=e00b11ac9c9b--01fdcd0pncfk9xmxbapkf6g9k7; b=.84fy986eugt8d4fgqdj719uf; shown_download_ssb_modal=1; optimizelyEndUserId=oeu1629388719548r0.3669633171888469; DriftPlaybook=B; __qca=P0-1592596921-1629388720339; show_download_ssb_banner=1; shown_ssb_redirect_page=1; OptanonAlertBoxClosed=2021-08-23T12:33:23.562Z; c={"banner_slack_russian_launch":1}; t={}; no_download_ssb_banner=1; _rdt_uuid=1632207704585.3443bf27-a8b7-40a3-adbb-bb26482f9285; ssb_instance_id=79029366-fa75-4fa7-b471-6910351ddcb7; _ga=GA1.3.261320561.1629282654; __pdst=b067848f57944c02bb35d3b0cf5e1423; d=DPBE5buc5HaQoX%2B2P61OHqCqkz5nhZgJY7dFxgku1CUJsyPRuSvGTqmj6Ojl%2FUltL4eBzeaRwDzEzAltFdVJv6JokPtwe3UD48myL58zwczgtpioP02mBaWlFOQz7v2mNcrUrZtk1G%2BzgRq8fusQALE1A0fg2%2F8sfM6b3mstaKcsZ52ey3z9Qx4xTA%3D%3D; d-s=1641906910; _gcl_au=1.1.1834466628.1647438518; __utmc=202413653; __utmz=202413653.1649921623.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmc=223732185; __utma=202413653.261320561.1629282654.1649930352.1650526176.3; _gid=GA1.2.1061591969.1651051988; __utma=223732185.261320561.1629282654.1651051988.1651137195.8; __utmz=223732185.1651137195.8.4.utmcsr=google|utmccn=(organic)|utmcmd=organic|utmctr=(not%20provided); PageCount=279; OptanonConsent=isGpcEnabled=0&datestamp=Thu+Apr+28+2022+12%3A14%3A23+GMT%2B0300+(Moscow+Standard+Time)&version=6.22.0&isIABGlobal=false&hosts=&consentId=7b41db92-36da-49e2-b56e-17393bd23d68&interactionCount=1&landingPath=NotLandingPage&groups=C0004%3A1%2CC0002%3A1%2CC0003%3A1%2CC0001%3A1&AwaitingReconsent=false&geolocation=RU%3BSPE; _ga=GA1.1.261320561.1629282654; _cs_id=b539ac9e-f882-a1e7-878b-255212370c11.1629282654.46.1651137264.1651137195.1.1663446654633; _ga_QTJQME5M5D=GS1.1.1651137195.44.1.1651138533.0; x=84fy986eugt8d4fgqdj719uf.1651138541' \
  -H 'origin: https://app.slack.com' \
  -H 'pragma: no-cache' \
  -H 'sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36' \
  --data-raw $'------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="channel"\r\n\r\nC02F5FBE397\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="limit"\r\n\r\n28\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="ignore_replies"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="include_pin_count"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="inclusive"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="no_user_profile"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="include_stories"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="token"\r\n\r\nxoxc-2496153549158-2515525842417-2526075683553-5b1f6a85415ba6740fe48391eb33507f420c7ccf4573ea3d1f07d586ee66c6c7\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="_x_reason"\r\n\r\nrequestOfflineHistory\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="_x_mode"\r\n\r\nonline\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c\r\nContent-Disposition: form-data; name="_x_sonic"\r\n\r\ntrue\r\n------WebKitFormBoundaryhuBPOcj6xHyC6o4c--\r\n' \
  --compressed ;

### Response

```json
{
    "ok": true,
    "messages": [
        {
            "client_msg_id": "e4d8cfa0-07d7-4cca-abe2-ae7d84bebd03",
            "type": "message",
            "text": "чтобы было 10 или больше",
            "user": "U02MBNVHPPS",
            "ts": "1650534052.441299",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "wnTeJ",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "чтобы было 10 или больше"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "d5053db9-8d5a-4dad-bfda-e08744c6a4e1",
            "type": "message",
            "text": "Галочку проставьте все пожалуйста верхнему сообщению",
            "user": "U02MBNVHPPS",
            "ts": "1650534046.540779",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "KwUW",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Галочку проставьте все пожалуйста верхнему сообщению"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "2e93ef53-25cc-49b2-b364-d2f4737c8ba3",
            "type": "message",
            "text": "Полайкайте это сообщение; Поставьте все пожалуйста зеленую галочку (самый первый лайк)",
            "user": "U02MBNVHPPS",
            "ts": "1650533872.483909",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02MBNVHPPS",
                "ts": "1650534019.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "tchK0",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Полайкайте это сообщение; Поставьте все пожалуйста зеленую галочку (самый первый лайк)"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1650533872.483909",
            "reply_count": 3,
            "reply_users_count": 2,
            "latest_reply": "1650615795.694449",
            "reply_users": [
                "U02F5FFQSC9",
                "U02JYUGKMC1"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1650615795.694449",
            "reactions": [
                {
                    "name": "raised_hands",
                    "users": [
                        "U02MBNVHPPS",
                        "U02EL6LM80N",
                        "U02ESULTR42",
                        "U02F5FFQSC9"
                    ],
                    "count": 4
                },
                {
                    "name": "eyes",
                    "users": [
                        "U02EL6LM80N",
                        "U02F5FFQSC9"
                    ],
                    "count": 2
                },
                {
                    "name": "grinning",
                    "users": [
                        "U02ESULTR42",
                        "U02F5FFQSC9",
                        "U02EL6LM80N"
                    ],
                    "count": 3
                },
                {
                    "name": "heart",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "+1",
                    "users": [
                        "U02F5FFQSC9",
                        "U02EL6LM80N"
                    ],
                    "count": 2
                },
                {
                    "name": "white_check_mark",
                    "users": [
                        "U02F5FFQSC9",
                        "U02EZ5GKC68",
                        "U02EL6LM80N",
                        "U02ESULTR42",
                        "U02ESUG4LQ2",
                        "U02EZ5C42DA",
                        "U02EC7EB17H",
                        "U02JDJPJSTG",
                        "U02JYUGKMC1",
                        "U02EPU2QVLM",
                        "U02MBNVHPPS"
                    ],
                    "count": 11
                },
                {
                    "name": "hankey",
                    "users": [
                        "U02EC7EB17H",
                        "U02ESULTR42",
                        "U02EL6LM80N"
                    ],
                    "count": 3
                },
                {
                    "name": "nerd_face",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "smiling_imp",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "tongue",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "scream_cat",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "space_invader",
                    "users": [
                        "U02EL6LM80N"
                    ],
                    "count": 1
                },
                {
                    "name": "radioactive_sign",
                    "users": [
                        "U02ESULTR42",
                        "U02EL6LM80N"
                    ],
                    "count": 2
                },
                {
                    "name": "bear",
                    "users": [
                        "U02ESULTR42"
                    ],
                    "count": 1
                },
                {
                    "name": "grin",
                    "users": [
                        "U02EZ5GKC68"
                    ],
                    "count": 1
                },
                {
                    "name": "mega",
                    "users": [
                        "U02JYUGKMC1"
                    ],
                    "count": 1
                },
                {
                    "name": "red_circle",
                    "users": [
                        "U02JYUGKMC1"
                    ],
                    "count": 1
                },
                {
                    "name": "white_circle",
                    "users": [
                        "U02JYUGKMC1"
                    ],
                    "count": 1
                },
                {
                    "name": "large_blue_circle",
                    "users": [
                        "U02JYUGKMC1"
                    ],
                    "count": 1
                }
            ]
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1650528471.118889",
            "user": "U02JDJPJSTG",
            "text": "Пользователь <@U02JDJPJSTG> присоединился к каналу."
        },
        {
            "client_msg_id": "1c1bbdc5-c627-4bc6-8025-67a38e76300c",
            "type": "message",
            "text": "Коллеги, важная информация.\nВ ходе <https://github.com/heeus/core-istructsmem/pull/83|доработок по ViewRecords.PutBatch> нашлась ошибка в хранении viewRecords, которая приводила к неверному порядку перебора записей, в сортировке (clustering columns) которых участвуют многобайтовые целочисленные значения (int32, int64 и т.д.). Ошибка исправлена, но данные в кассандре в очередной раз придется перезаливать.",
            "user": "U02EPU2QVLM",
            "ts": "1643902010.578029",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "LBn8M",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Коллеги, важная информация.\nВ ходе "
                                },
                                {
                                    "type": "link",
                                    "url": "https://github.com/heeus/core-istructsmem/pull/83",
                                    "text": "доработок по ViewRecords.PutBatch",
                                    "unsafe": true
                                },
                                {
                                    "type": "text",
                                    "text": " нашлась ошибка в хранении viewRecords, которая приводила к неверному порядку перебора записей, в сортировке (clustering columns) которых участвуют многобайтовые целочисленные значения (int32, int64 и т.д.). Ошибка исправлена, но данные в кассандре в очередной раз придется перезаливать."
                                }
                            ]
                        }
                    ]
                }
            ],
            "reactions": [
                {
                    "name": "white_check_mark",
                    "users": [
                        "U02F5FFQSC9",
                        "U02EC7EB17H"
                    ],
                    "count": 2
                }
            ]
        },
        {
            "client_msg_id": "c43337b0-2732-4c57-aafa-1f59dd820cde",
            "type": "message",
            "text": "Коллеги, важная информация.\nПеределки в istructs-mem PLog-а и WLog-а в последних двух моих commit-ах снова превратили все ваши данные в тыкву.\nВсе записанные до обновления в кассандру *журналы после обновления перестанут читаться*. БД надо будет пересоздавать.\n\nТоже самое произойдет в ближайшее время еще раз (сегодня вечером или завтра утром) — после экстракции QNameID из слоя istorage на уровень istructs.",
            "user": "U02EPU2QVLM",
            "ts": "1642421389.002200",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02EPU2QVLM",
                "ts": "1642421413.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "s2/Aw",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Коллеги, важная информация.\nПеределки в istructs-mem PLog-а и WLog-а в последних двух моих commit-ах снова превратили все ваши данные в тыкву.\nВсе записанные до обновления в кассандру "
                                },
                                {
                                    "type": "text",
                                    "text": "журналы после обновления перестанут читаться",
                                    "style": {
                                        "bold": true
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": ". БД надо будет пересоздавать.\n\nТоже самое произойдет в ближайшее время еще раз (сегодня вечером или завтра утром) — после экстракции QNameID из слоя istorage на уровень istructs."
                                }
                            ]
                        }
                    ]
                }
            ],
            "reactions": [
                {
                    "name": "+1",
                    "users": [
                        "U02ESULTR42",
                        "U02MBNVHPPS"
                    ],
                    "count": 2
                }
            ]
        },
        {
            "client_msg_id": "853b5fbb-2656-43d9-95e5-2c97edb3d4fd",
            "type": "message",
            "text": "Всех сердечно поздравляю с Новым годом!!!",
            "user": "U02F5FFQSC9",
            "ts": "1641206348.000900",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02F5FFQSC9",
                "ts": "1641466575.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "r+k",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Всех сердечно поздравляю с Новым годом!!!"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1641206348.000900",
            "reply_count": 5,
            "reply_users_count": 2,
            "latest_reply": "1650528680.468979",
            "reply_users": [
                "U02EPU2QVLM",
                "U02MBNVHPPS"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1650528680.468979",
            "reactions": [
                {
                    "name": "+1",
                    "users": [
                        "U02EPU2QVLM"
                    ],
                    "count": 1
                }
            ]
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1636442354.000400",
            "user": "U02MBNVHPPS",
            "text": "Пользователь <@U02MBNVHPPS> присоединился к каналу."
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1634827611.000400",
            "user": "U02JYUGKMC1",
            "text": "Пользователь <@U02JYUGKMC1> присоединился к каналу."
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1633956882.000500",
            "user": "U02GTQ92TB8",
            "text": "Пользователь <@U02GTQ92TB8> присоединился к каналу."
        },
        {
            "client_msg_id": "1ebd61fd-8621-4016-b8b4-c9fb102b8bc3",
            "type": "message",
            "text": "давай TV ))",
            "user": "U02ESULTR42",
            "ts": "1632832239.003300",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "f/i",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "давай TV ))"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "2a53f2c2-a8f1-4764-a0c1-884f6e87ffe1",
            "type": "message",
            "text": "<@U02EL6LM80N> <@U02EC7EB17H> <@U02EZ5GKC68> <@U02ESULTR42> коллеги, кто-то из вас любезно предлагал мне помочь с правильным набором плагинов для vsCode…\nМучаюсь с шумным статическим анализатором, с кривоватым autocomplette-ом кода",
            "user": "U02EPU2QVLM",
            "ts": "1632832210.003000",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "n3i",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "user",
                                    "user_id": "U02EL6LM80N"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EC7EB17H"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EZ5GKC68"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02ESULTR42"
                                },
                                {
                                    "type": "text",
                                    "text": " коллеги, кто-то из вас любезно предлагал мне помочь с правильным набором плагинов для vsCode…\nМучаюсь с шумным статическим анализатором, с кривоватым autocomplette-ом кода"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632832210.003000",
            "reply_count": 5,
            "reply_users_count": 2,
            "latest_reply": "1641820251.001800",
            "reply_users": [
                "U02F5FFQSC9",
                "U02EPU2QVLM"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1641820251.001800"
        },
        {
            "client_msg_id": "9f70a9e1-89c9-4a22-92ba-06b748c56ffb",
            "type": "message",
            "text": "<@U02ESUG4LQ2> <@U02EZ5C42DA> <@U02EPU2QVLM> В github вам выслано приглашение на чтение некоторых репозиториев в untillpro, примите его.",
            "user": "U02F5FFQSC9",
            "ts": "1632738395.002600",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "eos",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "user",
                                    "user_id": "U02ESUG4LQ2"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EZ5C42DA"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EPU2QVLM"
                                },
                                {
                                    "type": "text",
                                    "text": " В github вам выслано приглашение на чтение некоторых репозиториев в untillpro, примите его."
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "0bded86e-a941-46cb-a0dc-f06f68dbcf25",
            "type": "message",
            "text": "<!channel> Коллеги, опять некоторые изменения. \"Летучки\" будут проводится в 13:00 через Skype. Это краткое мероприятие на 5-10 минут, далее спокойно идем на обед. Группу в Skype создам и всех добавлю.",
            "user": "U02F5FFQSC9",
            "ts": "1632738351.001700",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "Prsze",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": " Коллеги, опять некоторые изменения. \"Летучки\" будут проводится в 13:00 через Skype. Это краткое мероприятие на 5-10 минут, далее спокойно идем на обед. Группу в Skype создам и всех добавлю."
                                }
                            ]
                        }
                    ]
                }
            ],
            "reactions": [
                {
                    "name": "+1",
                    "users": [
                        "U02EPU2QVLM"
                    ],
                    "count": 1
                }
            ]
        },
        {
            "client_msg_id": "34ab81af-fc82-47f5-bdf2-556850136c34",
            "type": "message",
            "text": "<!channel> Коллеги, в 11:00 неудобно летучку делать, часто придется отменять. Давайте 13:00, продолжительность около 10 минут?",
            "user": "U02F5FFQSC9",
            "ts": "1632482306.012300",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02F5FFQSC9",
                "ts": "1632484741.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "MeWxH",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": " Коллеги, в 11:00 неудобно летучку делать, часто придется отменять. Давайте 13:00, продолжительность около 10 минут?"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632482306.012300",
            "reply_count": 29,
            "reply_users_count": 2,
            "latest_reply": "1650528964.499289",
            "reply_users": [
                "U02EPU2QVLM",
                "U02F5FFQSC9"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1650528964.499289",
            "reactions": [
                {
                    "name": "+1",
                    "users": [
                        "U02EPU2QVLM"
                    ],
                    "count": 1
                }
            ]
        },
        {
            "client_msg_id": "ffaa98f8-3015-463f-91e5-af5fb3ee78b8",
            "type": "message",
            "text": "<@U02EPU2QVLM> <@U02EZ5C42DA> <@U02ESUG4LQ2>\n• <https://tour.golang.org/welcome/1>\n• <https://golang.org/doc/effective_go>\n",
            "user": "U02F5FFQSC9",
            "ts": "1632400925.009900",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "ME4W",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "user",
                                    "user_id": "U02EPU2QVLM"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EZ5C42DA"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02ESUG4LQ2"
                                },
                                {
                                    "type": "text",
                                    "text": "\n"
                                }
                            ]
                        },
                        {
                            "type": "rich_text_list",
                            "elements": [
                                {
                                    "type": "rich_text_section",
                                    "elements": [
                                        {
                                            "type": "link",
                                            "url": "https://tour.golang.org/welcome/1"
                                        }
                                    ]
                                },
                                {
                                    "type": "rich_text_section",
                                    "elements": [
                                        {
                                            "type": "link",
                                            "url": "https://golang.org/doc/effective_go"
                                        }
                                    ]
                                }
                            ],
                            "style": "bullet",
                            "indent": 0,
                            "border": 0
                        },
                        {
                            "type": "rich_text_section",
                            "elements": []
                        }
                    ]
                }
            ],
            "reactions": [
                {
                    "name": "+1",
                    "users": [
                        "U02EPU2QVLM"
                    ],
                    "count": 1
                }
            ]
        },
        {
            "client_msg_id": "d29ac330-e174-4c01-91f2-b173da808e70",
            "type": "message",
            "text": "<!channel> Завтра (23.09) \"летучка\" в 11:00 отменяется",
            "user": "U02F5FFQSC9",
            "ts": "1632325445.008700",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02F5FFQSC9",
                "ts": "1632473735.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "QBSgq",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": " Завтра (23.09) \"летучка\" в 11:00 отменяется"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632325445.008700",
            "reply_count": 1,
            "reply_users_count": 1,
            "latest_reply": "1632470827.010700",
            "reply_users": [
                "U02EPU2QVLM"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1632470827.010700"
        },
        {
            "client_msg_id": "65068f52-9a9f-44cf-b2a0-864342efe0f7",
            "type": "message",
            "text": "<@U02EPU2QVLM> <@U02EZ5C42DA> Завтра во сколько? 11:00?",
            "user": "U02F5FFQSC9",
            "ts": "1632322414.007400",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "Glun",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "user",
                                    "user_id": "U02EPU2QVLM"
                                },
                                {
                                    "type": "text",
                                    "text": " "
                                },
                                {
                                    "type": "user",
                                    "user_id": "U02EZ5C42DA"
                                },
                                {
                                    "type": "text",
                                    "text": " Завтра во сколько? 11:00?"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632322414.007400",
            "reply_count": 2,
            "reply_users_count": 2,
            "latest_reply": "1632324099.007700",
            "reply_users": [
                "U02EPU2QVLM",
                "U02EZ5C42DA"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1632325385.007900"
        },
        {
            "client_msg_id": "69aaee7f-62e1-42a8-af22-87756851332e",
            "type": "message",
            "text": "<@U02F5FFQSC9> SSE и его основные отличия от WebSocket: <https://learn.javascript.ru/server-sent-events>",
            "user": "U02ESUG4LQ2",
            "ts": "1632230360.004100",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02ESUG4LQ2",
                "ts": "1632230396.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "ypS",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "user",
                                    "user_id": "U02F5FFQSC9"
                                },
                                {
                                    "type": "text",
                                    "text": " SSE и его основные отличия от WebSocket: "
                                },
                                {
                                    "type": "link",
                                    "url": "https://learn.javascript.ru/server-sent-events"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632230360.004100",
            "reply_count": 6,
            "reply_users_count": 2,
            "latest_reply": "1632316342.006700",
            "reply_users": [
                "U02F5FFQSC9",
                "U02ESUG4LQ2"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1632316342.006700",
            "is_starred": true
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1632211936.002800",
            "user": "U02ESULTR42",
            "text": "Пользователь <@U02ESULTR42> присоединился к каналу."
        },
        {
            "client_msg_id": "05a76682-0eb0-4bf4-8ee7-52ab968f05d8",
            "type": "message",
            "text": "<!channel> \"Летучки\" будут проводится в 13:00 через Skype. Это краткое мероприятие на 5-10 минут, далее спокойно идем на обед. Группу в Skype создам и всех добавлю.",
            "user": "U02F5FFQSC9",
            "ts": "1632208245.002100",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02F5FFQSC9",
                "ts": "1632738807.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "bmcy",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": " \"Летучки\" будут проводится в 13:00 через Skype. Это краткое мероприятие на 5-10 минут, далее спокойно идем на обед. Группу в Skype создам и всех добавлю."
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1632208245.002100",
            "reply_count": 2,
            "reply_users_count": 2,
            "latest_reply": "1632216001.002900",
            "reply_users": [
                "U02EPU2QVLM",
                "U02EZ5C42DA"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1632216001.002900",
            "pinned_to": [
                "C02F5FBE397"
            ],
            "pinned_info": {
                "channel": "C02F5FBE397",
                "pinned_by": "U02F5FFQSC9",
                "pinned_ts": 1632297467
            },
            "reactions": [
                {
                    "name": "heavy_plus_sign",
                    "users": [
                        "U02EC7EB17H",
                        "U02F5FFQSC9"
                    ],
                    "count": 2
                }
            ]
        },
        {
            "client_msg_id": "0f48fda8-8505-4a3e-bf48-7c47e9c09190",
            "type": "message",
            "text": "<mailto:<EMAIL>|<EMAIL>>",
            "user": "U02F5LTV6G1",
            "ts": "1631876237.010000",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "h0mC",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "link",
                                    "url": "mailto:<EMAIL>",
                                    "text": "<EMAIL>",
                                    "unsafe": true
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "8cabe6f8-7572-4602-ab33-fa5af531926d",
            "type": "message",
            "text": "AlexeySim",
            "user": "U02F5LTV6G1",
            "ts": "1631876216.009500",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "hUE",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "AlexeySim"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "9f9d153e-0b9f-4941-97ee-2c6f32099cfc",
            "type": "message",
            "text": "email : <mailto:<EMAIL>|<EMAIL>>",
            "user": "U02EZ5C42DA",
            "ts": "1631875985.009300",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "aX0Q+",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "email : "
                                },
                                {
                                    "type": "link",
                                    "url": "mailto:<EMAIL>",
                                    "text": "<EMAIL>",
                                    "unsafe": true
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "client_msg_id": "5f6bc3a9-727d-4313-92e6-49bfb410147d",
            "type": "message",
            "text": "login: dmitrymolchanovsky",
            "user": "U02EZ5C42DA",
            "ts": "1631875967.008900",
            "team": "T02EL4HG54N",
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "KjL",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "login: dmitrymolchanovsky"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1631875967.008900",
            "reply_count": 1,
            "reply_users_count": 1,
            "latest_reply": "1641815252.001000",
            "reply_users": [
                "U02EPU2QVLM"
            ],
            "is_locked": false,
            "subscribed": false
        },
        {
            "client_msg_id": "02d135bb-0eed-4625-ab6e-51d5e5b76649",
            "type": "message",
            "text": "Господа из Сигма-софт, нужны ваши логины/emails в <http://Github.com|Github.com>\nВсе на нем зарегистрированы?",
            "user": "U02EZ5GKC68",
            "ts": "1631873860.006900",
            "team": "T02EL4HG54N",
            "edited": {
                "user": "U02EZ5GKC68",
                "ts": "1631875534.000000"
            },
            "blocks": [
                {
                    "type": "rich_text",
                    "block_id": "gERq",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": "Господа из Сигма-софт, нужны ваши логины/emails в "
                                },
                                {
                                    "type": "link",
                                    "url": "http://Github.com",
                                    "text": "Github.com"
                                },
                                {
                                    "type": "text",
                                    "text": "\nВсе на нем зарегистрированы?"
                                }
                            ]
                        }
                    ]
                }
            ],
            "thread_ts": "1631873860.006900",
            "reply_count": 7,
            "reply_users_count": 4,
            "latest_reply": "1632116256.000300",
            "reply_users": [
                "U02F5FFQSC9",
                "U02EPU2QVLM",
                "U02EZ5GKC68",
                "U02ESUG4LQ2"
            ],
            "is_locked": false,
            "subscribed": true,
            "last_read": "1632116256.000300"
        },
        {
            "type": "message",
            "subtype": "channel_join",
            "ts": "1631871449.005100",
            "user": "U02F5LTV6G1",
            "text": "Пользователь <@U02F5LTV6G1> присоединился к каналу."
        },
        {
            "type": "message",
            "text": "",
            "files": [
                {
                    "id": "F02FGKBT0QG",
                    "created": 1631870522,
                    "timestamp": 1631870522,
                    "name": "image.png",
                    "title": "image.png",
                    "mimetype": "image/png",
                    "filetype": "png",
                    "pretty_type": "PNG",
                    "user": "U02F5FFQSC9",
                    "editable": false,
                    "size": 113014,
                    "mode": "hosted",
                    "is_external": false,
                    "external_type": "",
                    "is_public": true,
                    "public_url_shared": false,
                    "display_as_bot": false,
                    "username": "",
                    "url_private": "https://files.slack.com/files-pri/T02EL4HG54N-F02FGKBT0QG/image.png",
                    "url_private_download": "https://files.slack.com/files-pri/T02EL4HG54N-F02FGKBT0QG/download/image.png",
                    "media_display_type": "unknown",
                    "thumb_64": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_64.png",
                    "thumb_80": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_80.png",
                    "thumb_360": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_360.png",
                    "thumb_360_w": 360,
                    "thumb_360_h": 187,
                    "thumb_480": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_480.png",
                    "thumb_480_w": 480,
                    "thumb_480_h": 250,
                    "thumb_160": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_160.png",
                    "thumb_720": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_720.png",
                    "thumb_720_w": 720,
                    "thumb_720_h": 375,
                    "thumb_800": "https://files.slack.com/files-tmb/T02EL4HG54N-F02FGKBT0QG-0122a64c70/image_800.png",
                    "thumb_800_w": 800,
                    "thumb_800_h": 417,
                    "original_w": 868,
                    "original_h": 452,
                    "thumb_tiny": "AwAYADC6dx5BxSgEnBdgfTinqMrilIBGDRcBnlt/z0P5CmHOflYn37VNtBGDz9ajkjVmyQfzouA0RHqWOfpTTIFbDSkH6UvkoGGc4PH3jVW4QmU8EjjPPtTuFjRTpS0idKWkAU09adTT1oASoXjUs2c8+1TU09aQz//Z",
                    "permalink": "https://untillairheeus.slack.com/files/U02F5FFQSC9/F02FGKBT0QG/image.png",
                    "permalink_public": "https://slack-files.com/T02EL4HG54N-F02FGKBT0QG-99d696401b",
                    "is_starred": false,
                    "has_rich_preview": false
                }
            ],
            "upload": false,
            "user": "U02F5FFQSC9",
            "display_as_bot": false,
            "ts": "1631870524.004600"
        }
    ],
    "has_more": true,
    "pin_count": 1,
    "channel_actions_ts": null,
    "channel_actions_count": 0,
    "response_metadata": {
        "next_cursor": "bmV4dF90czoxNjMxODcwNTExMDA0NTAw"
    }
}
```